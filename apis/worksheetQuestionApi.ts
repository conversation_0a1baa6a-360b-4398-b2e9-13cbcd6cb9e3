import { EAPIEndpoint } from "@/@types/enums/api";
import { request } from "./request";
import { TTransformResponse } from "./transformResponse";

// Question Types
export interface IWorksheetQuestion {
  id: string;
  type: 'MULTIPLE_CHOICE' | 'TRUE_FALSE' | 'FILL_IN_BLANK' | 'SHORT_ANSWER' | 'ESSAY' | 'MATCHING' | 'ORDERING';
  content: string;
  options: string[];
  answer: string[];
  explain: string;
  position?: number;
  points?: number;
  subject?: string;
  parentSubject?: string;
  childSubject?: string;
  topic?: string;
  subtopic?: string;
  grade?: string;
  difficulty?: 'EASY' | 'MEDIUM' | 'HARD';
  status?: 'ACTIVE' | 'INACTIVE' | 'DRAFT' | 'ARCHIVED';
  isPublic?: boolean;
  media?: {
    imageUrl?: string;
    imagePrompt?: string;
    audioUrl?: string;
    videoUrl?: string;
    attachmentUrls?: string[];
  };
  imagePrompt?: string;
  imageUrl?: string;
  metadata?: {
    tags?: string[];
    keywords?: string[];
    estimatedTimeMinutes?: number;
    cognitiveLevel?: string;
    learningObjectives?: string[];
    prerequisites?: string[];
    hints?: string[];
    references?: string[];
    authorNotes?: string;
    reviewNotes?: string;
    lastReviewDate?: Date;
    nextReviewDate?: Date;
  };
  createdAt?: Date;
  updatedAt?: Date;
  version?: number;
}

// Request DTOs
export interface IAddQuestionToWorksheetDto {
  type: 'MULTIPLE_CHOICE' | 'TRUE_FALSE' | 'FILL_IN_BLANK' | 'SHORT_ANSWER' | 'ESSAY' | 'MATCHING' | 'ORDERING';
  content: string;
  options: string[];
  answer: string[];
  explain: string;
  position?: number;
  points?: number;
  subject?: string;
  parentSubject?: string;
  childSubject?: string;
  topic?: string;
  subtopic?: string;
  grade?: string;
  difficulty?: 'EASY' | 'MEDIUM' | 'HARD';
  status?: 'ACTIVE' | 'INACTIVE' | 'DRAFT' | 'ARCHIVED';
  isPublic?: boolean;
  media?: {
    imageUrl?: string;
    imagePrompt?: string;
    audioUrl?: string;
    videoUrl?: string;
    attachmentUrls?: string[];
  };
  imagePrompt?: string;
  imageUrl?: string;
  metadata?: {
    tags?: string[];
    keywords?: string[];
    estimatedTimeMinutes?: number;
    cognitiveLevel?: string;
    learningObjectives?: string[];
    prerequisites?: string[];
    hints?: string[];
    references?: string[];
    authorNotes?: string;
    reviewNotes?: string;
    lastReviewDate?: Date;
    nextReviewDate?: Date;
  };
}

export interface IUpdateWorksheetQuestionDto {
  content?: string;
  options?: string[];
  answer?: string[];
  explain?: string;
  position?: number;
  points?: number;
  subject?: string;
  parentSubject?: string;
  childSubject?: string;
  topic?: string;
  subtopic?: string;
  grade?: string;
  difficulty?: 'EASY' | 'MEDIUM' | 'HARD';
  status?: 'ACTIVE' | 'INACTIVE' | 'DRAFT' | 'ARCHIVED';
  isPublic?: boolean;
  media?: {
    imageUrl?: string;
    imagePrompt?: string;
    audioUrl?: string;
    videoUrl?: string;
    attachmentUrls?: string[];
  };
  imagePrompt?: string;
  imageUrl?: string;
  metadata?: {
    tags?: string[];
    keywords?: string[];
    estimatedTimeMinutes?: number;
    cognitiveLevel?: string;
    learningObjectives?: string[];
    prerequisites?: string[];
    hints?: string[];
    references?: string[];
    authorNotes?: string;
    reviewNotes?: string;
    lastReviewDate?: Date;
    nextReviewDate?: Date;
  };
  version?: number;
  updateReason?: string;
}

export interface IReplaceWorksheetQuestionDto extends IAddQuestionToWorksheetDto {
  version: number;
  updateReason?: string;
}

export interface IReorderQuestionDto {
  questionId: string;
  newPosition: number;
}

export interface IBulkReorderQuestionsDto {
  reorders: IReorderQuestionDto[];
}

export interface IBulkAddQuestionsDto {
  questions: IAddQuestionToWorksheetDto[];
  insertPosition?: number;
  validateQuestions?: boolean;
  reason?: string;
}

export interface IBulkRemoveQuestionsDto {
  questionIds: string[];
  reason?: string;
  forceRemoval?: boolean;
}

export interface IBulkQuestionUpdateItem {
  questionId: string;
  updates: IUpdateWorksheetQuestionDto;
}

export interface IBulkUpdateQuestionsDto {
  updates: IBulkQuestionUpdateItem[];
  reason?: string;
  validateQuestions?: boolean;
}

// Response DTOs
export interface IAddQuestionResponse {
  questionId: string;
  position: number;
  worksheetId: string;
  totalQuestions: number;
  message: string;
}

export interface IRemoveQuestionResponse {
  removedQuestionId: string;
  worksheetId: string;
  totalQuestions: number;
  positionsUpdated: Array<{
    questionId: string;
    oldPosition: number;
    newPosition: number;
  }>;
  message: string;
}

export interface IUpdateQuestionResponse {
  questionId: string;
  version: number;
  updatedFields: string[];
  worksheetId: string;
  message: string;
}

export interface IReplaceQuestionResponse {
  questionId: string;
  version: number;
  worksheetId: string;
  message: string;
}

export interface IReorderResponse {
  worksheetId: string;
  reorderedQuestions: Array<{
    questionId: string;
    oldPosition: number;
    newPosition: number;
  }>;
  totalQuestions: number;
  message: string;
}

export interface IBulkOperationResponse {
  success: boolean;
  successCount: number;
  failureCount: number;
  totalCount: number;
  successes?: Array<{
    questionId: string;
    position: number;
    index: number;
  }>;
  failures?: Array<{
    item: any;
    error: string;
    index: number;
  }>;
  timestamp: string;
  processingTimeMs: number;
}

export interface IBulkRemoveResponse extends IBulkOperationResponse {
  data: {
    removedQuestionIds: string[];
    positionsUpdated: Array<{
      questionId: string;
      oldPosition: number;
      newPosition: number;
    }>;
    totalQuestions: number;
  };
}

export interface IBulkUpdateResponse extends IBulkOperationResponse {
  data: {
    updatedQuestions: Array<{
      questionId: string;
      version: number;
      updatedFields: string[];
    }>;
  };
}

// API Functions
const WORKSHEET_QUESTIONS_API_ENDPOINT = EAPIEndpoint.WORKSHEET_QUESTIONS;

/**
 * Add a question to a worksheet
 * Corresponds to: POST /worksheets/:worksheetId/questions
 */
export async function addQuestionToWorksheet(
  worksheetId: string,
  questionData: IAddQuestionToWorksheetDto
): Promise<TTransformResponse<IAddQuestionResponse>> {
  try {
    const response = await request<IAddQuestionResponse>({
      url: `${WORKSHEET_QUESTIONS_API_ENDPOINT}/${worksheetId}/questions`,
      options: {
        method: 'POST',
        body: JSON.stringify(questionData),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error adding question to worksheet:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while adding the question.'
    };
  }
}

/**
 * Remove a question from a worksheet
 * Corresponds to: DELETE /worksheets/:worksheetId/questions/:questionId
 */
export async function removeQuestionFromWorksheet(
  worksheetId: string,
  questionId: string,
  reason?: string
): Promise<TTransformResponse<IRemoveQuestionResponse>> {
  try {
    let url = `${WORKSHEET_QUESTIONS_API_ENDPOINT}/${worksheetId}/questions/${questionId}`;
    if (reason) {
      url += `?reason=${encodeURIComponent(reason)}`;
    }

    const response = await request<IRemoveQuestionResponse>({
      url,
      options: {
        method: 'DELETE',
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error removing question from worksheet:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while removing the question.'
    };
  }
}

/**
 * Update a question in a worksheet (partial update)
 * Corresponds to: PATCH /worksheets/:worksheetId/questions/:questionId
 */
export async function updateQuestionInWorksheet(
  worksheetId: string,
  questionId: string,
  updateData: IUpdateWorksheetQuestionDto
): Promise<TTransformResponse<IUpdateQuestionResponse>> {
  try {
    const response = await request<IUpdateQuestionResponse>({
      url: `${WORKSHEET_QUESTIONS_API_ENDPOINT}/${worksheetId}/questions/${questionId}`,
      options: {
        method: 'PATCH',
        body: JSON.stringify(updateData),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error updating question in worksheet:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while updating the question.'
    };
  }
}

/**
 * Replace a question in a worksheet (complete replacement)
 * Corresponds to: PUT /worksheets/:worksheetId/questions/:questionId
 */
export async function replaceQuestionInWorksheet(
  worksheetId: string,
  questionId: string,
  questionData: IReplaceWorksheetQuestionDto
): Promise<TTransformResponse<IReplaceQuestionResponse>> {
  try {
    const response = await request<IReplaceQuestionResponse>({
      url: `${WORKSHEET_QUESTIONS_API_ENDPOINT}/${worksheetId}/questions/${questionId}`,
      options: {
        method: 'PUT',
        body: JSON.stringify(questionData),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error replacing question in worksheet:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while replacing the question.'
    };
  }
}

/**
 * Reorder questions in a worksheet
 * Corresponds to: PATCH /worksheets/:worksheetId/questions/reorder
 */
export async function reorderQuestionsInWorksheet(
  worksheetId: string,
  reorderData: IReorderQuestionDto | IBulkReorderQuestionsDto
): Promise<TTransformResponse<IReorderResponse>> {
  try {
    const response = await request<IReorderResponse>({
      url: `${WORKSHEET_QUESTIONS_API_ENDPOINT}/${worksheetId}/questions/reorder`,
      options: {
        method: 'PATCH',
        body: JSON.stringify(reorderData),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error reordering questions in worksheet:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while reordering questions.'
    };
  }
}

/**
 * Bulk add questions to a worksheet
 * Corresponds to: POST /worksheets/:worksheetId/questions/bulk
 */
export async function bulkAddQuestionsToWorksheet(
  worksheetId: string,
  bulkData: IBulkAddQuestionsDto
): Promise<TTransformResponse<IBulkOperationResponse>> {
  try {
    const response = await request<IBulkOperationResponse>({
      url: `${WORKSHEET_QUESTIONS_API_ENDPOINT}/${worksheetId}/questions/bulk`,
      options: {
        method: 'POST',
        body: JSON.stringify(bulkData),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error bulk adding questions to worksheet:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while bulk adding questions.'
    };
  }
}

/**
 * Bulk remove questions from a worksheet
 * Corresponds to: DELETE /worksheets/:worksheetId/questions/bulk
 */
export async function bulkRemoveQuestionsFromWorksheet(
  worksheetId: string,
  bulkData: IBulkRemoveQuestionsDto
): Promise<TTransformResponse<IBulkRemoveResponse>> {
  try {
    const response = await request<IBulkRemoveResponse>({
      url: `${WORKSHEET_QUESTIONS_API_ENDPOINT}/${worksheetId}/questions/bulk`,
      options: {
        method: 'DELETE',
        body: JSON.stringify(bulkData),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error bulk removing questions from worksheet:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while bulk removing questions.'
    };
  }
}

/**
 * Bulk update questions in a worksheet
 * Corresponds to: PATCH /worksheets/:worksheetId/questions/bulk
 */
export async function bulkUpdateQuestionsInWorksheet(
  worksheetId: string,
  bulkData: IBulkUpdateQuestionsDto
): Promise<TTransformResponse<IBulkUpdateResponse>> {
  try {
    const response = await request<IBulkUpdateResponse>({
      url: `${WORKSHEET_QUESTIONS_API_ENDPOINT}/${worksheetId}/questions/bulk`,
      options: {
        method: 'PATCH',
        body: JSON.stringify(bulkData),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error bulk updating questions in worksheet:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while bulk updating questions.'
    };
  }
}
