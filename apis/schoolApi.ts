import { request } from './request'; // Assuming a generic request helper
import { TTransformResponse } from './transformResponse'; // For response typing
import { getCurrentUser } from './userApi';

export interface ICreateSchoolPayload {
  name: string;
  address?: string;
  phoneNumber?: string;
  registeredNumber?: string;
  email?: string;
  adminId?: string;
  brandId?: string;
}

export interface IUpdateSchoolPayload {
  name?: string;
  address?: string;
  phoneNumber?: string;
  registeredNumber?: string;
  email?: string;
  brandId?: string;
}

export interface ISchoolResponse {
  id: string;
  name: string;
  address: string;
  phoneNumber: string;
  registeredNumber: string;
  email: string;
  admin?: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  brand?: {
    id: string;
    logo?: string;
    color?: string;
    image?: string;
  };
  createdAt?: string;
  updatedAt?: string;
  examinationFormat?: {
    uploadedAt?: string;
    uploader?: string;
    fileSize?: number;
  };
  narrativeStructure?: {
    createdAt?: string;
    extractedBy?: string;
    status?: string;
  };
}

export interface IAssignSchoolManagerPayload {
  adminId: string;
}

const SCHOOLS_API_ENDPOINT = '/schools'; // As per documentation

/**
 * Creates a new school.
 * Corresponds to: POST /schools
 * @param payload - The school data.
 * @returns The created school details.
 */
export async function createSchool(payload: ICreateSchoolPayload): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    const response = await request<ISchoolResponse>({
      url: SCHOOLS_API_ENDPOINT,
      options: {
        method: 'POST',
        body: JSON.stringify(payload),
      },
    });
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected error occurred while creating the school.' };
  }
}

/**
 * Assigns a school manager to a school (updates the school's adminId).
 * Corresponds to: PATCH /schools/{id}
 * @param schoolId - The ID of the school to update.
 * @param payload - The data containing the adminId to assign.
 * @returns The updated school details.
 */
export async function assignSchoolManager(schoolId: string, payload: IAssignSchoolManagerPayload): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    const response = await request<ISchoolResponse>({
      url: `${SCHOOLS_API_ENDPOINT}/${schoolId}`,
      options: {
        method: 'PATCH',
        body: JSON.stringify(payload),
      },
    });
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected error occurred while assigning the school manager.' };
  }
}

/**
 * Fetches all schools.
 * Corresponds to: GET /schools
 * @returns A list of all schools.
 */
export async function getAllSchools(): Promise<TTransformResponse<ISchoolResponse[]>> {
  try {
    const response = await request<ISchoolResponse[]>({
      url: SCHOOLS_API_ENDPOINT,
      options: {
        method: 'GET',
      },
    });
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected error occurred while fetching schools.' };
  }
}

/**
 * Fetches a school by ID.
 * Corresponds to: GET /schools/{id}
 * @param schoolId - The ID of the school to fetch.
 * @returns The school details.
 */
export async function getSchoolById(schoolId: string): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    const response = await request<ISchoolResponse>({
      url: `${SCHOOLS_API_ENDPOINT}/${schoolId}`,
      options: {
        method: 'GET',
      },
    });
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected error occurred while fetching the school.' };
  }
}

/**
 * Updates an existing school's information.
 * Corresponds to: PATCH /schools/{id}
 * @param schoolId - The ID of the school to update.
 * @param payload - The school data to update.
 * @returns The updated school details.
 */
export async function updateSchool(schoolId: string, payload: IUpdateSchoolPayload): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    const response = await request<ISchoolResponse>({
      url: `${SCHOOLS_API_ENDPOINT}/${schoolId}`,
      options: {
        method: 'PATCH',
        body: JSON.stringify(payload),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error updating school:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while updating the school.' };
  }
}

/**
 * Deletes a school by ID.
 * Corresponds to: DELETE /schools/{id}
 * @param schoolId - The ID of the school to delete.
 * @returns A success or error response.
 */
export async function deleteSchool(schoolId: string): Promise<TTransformResponse<null>> {
  try {
    const response = await request<null>({
      url: `${SCHOOLS_API_ENDPOINT}/${schoolId}`,
      options: {
        method: 'DELETE',
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error deleting school:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while deleting the school.' };
  }
}


const SCHOOL_API_ENDPOINT = '/schools';

/**
 * Creates a new school for INDEPENDENT_TEACHER.
 * Corresponds to: POST /school
 * @param payload - The school data.
 * @returns The created school details.
 */
export async function createSchoolIndependent(payload: ICreateSchoolPayload): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    console.log('createSchoolIndependent SCHOOL_API_ENDPOINT:', SCHOOL_API_ENDPOINT);
    const response = await request<ISchoolResponse>({
      url: SCHOOL_API_ENDPOINT,
      options: {
        method: 'POST',
        body: JSON.stringify(payload),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error creating school for independent teacher:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while creating the school.' };
  }
}

/**
 * Gets the current user's school.
 * Corresponds to: GET /schools/my-school (for INDEPENDENT_TEACHER)
 * @returns The user's school details or null if no school exists.
 */
export async function getMySchool(): Promise<TTransformResponse<ISchoolResponse | null>> {
  try {
    // Call the /schools/my-school endpoint directly for independent teachers
    const schoolResponse = await request<ISchoolResponse>({
      url: `${SCHOOL_API_ENDPOINT}/my-school`,
      options: {
        method: 'GET',
      },
    });

    return schoolResponse;
  } catch (error: any) {
    console.error('Error fetching my school:', error);
    // If it's a 404, return null instead of error (user has no school)
    if (error.message?.includes('404') || error.message?.includes('not found')) {
      return { status: 'success', data: null };
    }
    return { status: 'error', message: error.message || 'An unexpected error occurred while fetching your school.' };
  }
}

/**
 * Updates the current user's school.
 * Corresponds to: PATCH /schools/my-school
 * @param payload - The school data to update.
 * @returns The updated school details.
 */
export async function updateMySchool(payload: IUpdateSchoolPayload): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    const response = await request<ISchoolResponse>({
      url: `${SCHOOL_API_ENDPOINT}/my-school`,
      options: {
        method: 'PATCH',
        body: JSON.stringify(payload),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error updating my school:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while updating your school.' };
  }
}
