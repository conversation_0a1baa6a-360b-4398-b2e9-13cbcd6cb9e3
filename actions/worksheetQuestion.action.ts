'use server';

import {
  addQuestionToWorksheet,
  removeQuestionFromWorksheet,
  updateQuestionInWorksheet,
  replaceQuestionInWorksheet,
  reorderQuestionsInWorksheet,
  bulkAddQuestionsToWorksheet,
  bulkRemoveQuestionsFromWorksheet,
  bulkUpdateQuestionsInWorksheet,
  IAddQuestionToWorksheetDto,
  IUpdateWorksheetQuestionDto,
  IReplaceWorksheetQuestionDto,
  IReorderQuestionDto,
  IBulkReorderQuestionsDto,
  IBulkAddQuestionsDto,
  IBulkRemoveQuestionsDto,
  IBulkUpdateQuestionsDto,
} from '@/apis/worksheetQuestionApi';
import { TTransformResponse } from '@/apis/transformResponse';
import { revalidatePath } from 'next/cache';
import { ERoutes } from '@/config/enums/enum';

/**
 * Server Action to create a new question in a worksheet
 */
export async function handleCreateQuestionAction(
  worksheetId: string,
  questionData: IAddQuestionToWorksheetDto
): Promise<{ status: 'success' | 'error'; message?: string; data?: any }> {
  try {
    const response = await addQuestionToWorksheet(worksheetId, questionData);
    
    if (response.status === 'success') {
      // Revalidate the worksheet review page to reflect changes
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      
      return { 
        status: 'success', 
        message: response.data?.message || 'Question created successfully.',
        data: response.data
      };
    } else {
      return { 
        status: 'error', 
        message: response.message || 'Failed to create question.' 
      };
    }
  } catch (error: any) {
    console.error('Error in handleCreateQuestionAction:', error);
    return { 
      status: 'error', 
      message: error.message || 'An unexpected error occurred while creating the question.' 
    };
  }
}

/**
 * Server Action to update an existing question in a worksheet
 */
export async function handleUpdateQuestionAction(
  worksheetId: string,
  questionId: string,
  updateData: IUpdateWorksheetQuestionDto
): Promise<{ status: 'success' | 'error'; message?: string; data?: any }> {
  try {
    const response = await updateQuestionInWorksheet(worksheetId, questionId, updateData);
    
    if (response.status === 'success') {
      // Revalidate the worksheet review page to reflect changes
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      
      return { 
        status: 'success', 
        message: response.data?.message || 'Question updated successfully.',
        data: response.data
      };
    } else {
      return { 
        status: 'error', 
        message: response.message || 'Failed to update question.' 
      };
    }
  } catch (error: any) {
    console.error('Error in handleUpdateQuestionAction:', error);
    return { 
      status: 'error', 
      message: error.message || 'An unexpected error occurred while updating the question.' 
    };
  }
}

/**
 * Server Action to delete a question from a worksheet
 */
export async function handleDeleteQuestionAction(
  worksheetId: string,
  questionId: string,
  reason?: string
): Promise<{ status: 'success' | 'error'; message?: string; data?: any }> {
  try {
    const response = await removeQuestionFromWorksheet(worksheetId, questionId, reason);
    
    if (response.status === 'success') {
      // Revalidate the worksheet review page to reflect changes
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      
      return { 
        status: 'success', 
        message: response.data?.message || 'Question deleted successfully.',
        data: response.data
      };
    } else {
      return { 
        status: 'error', 
        message: response.message || 'Failed to delete question.' 
      };
    }
  } catch (error: any) {
    console.error('Error in handleDeleteQuestionAction:', error);
    return { 
      status: 'error', 
      message: error.message || 'An unexpected error occurred while deleting the question.' 
    };
  }
}

/**
 * Server Action to replace a question in a worksheet
 */
export async function handleReplaceQuestionAction(
  worksheetId: string,
  questionId: string,
  questionData: IReplaceWorksheetQuestionDto
): Promise<{ status: 'success' | 'error'; message?: string; data?: any }> {
  try {
    const response = await replaceQuestionInWorksheet(worksheetId, questionId, questionData);
    
    if (response.status === 'success') {
      // Revalidate the worksheet review page to reflect changes
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      
      return { 
        status: 'success', 
        message: response.data?.message || 'Question replaced successfully.',
        data: response.data
      };
    } else {
      return { 
        status: 'error', 
        message: response.message || 'Failed to replace question.' 
      };
    }
  } catch (error: any) {
    console.error('Error in handleReplaceQuestionAction:', error);
    return { 
      status: 'error', 
      message: error.message || 'An unexpected error occurred while replacing the question.' 
    };
  }
}

/**
 * Server Action to reorder questions in a worksheet
 */
export async function handleReorderQuestionsAction(
  worksheetId: string,
  reorderData: IReorderQuestionDto | IBulkReorderQuestionsDto
): Promise<{ status: 'success' | 'error'; message?: string; data?: any }> {
  try {
    const response = await reorderQuestionsInWorksheet(worksheetId, reorderData);
    
    if (response.status === 'success') {
      // Revalidate the worksheet review page to reflect changes
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      
      return { 
        status: 'success', 
        message: response.data?.message || 'Questions reordered successfully.',
        data: response.data
      };
    } else {
      return { 
        status: 'error', 
        message: response.message || 'Failed to reorder questions.' 
      };
    }
  } catch (error: any) {
    console.error('Error in handleReorderQuestionsAction:', error);
    return { 
      status: 'error', 
      message: error.message || 'An unexpected error occurred while reordering questions.' 
    };
  }
}

/**
 * Server Action to duplicate a question in a worksheet
 */
export async function handleDuplicateQuestionAction(
  worksheetId: string,
  questionId: string,
  questionData: IAddQuestionToWorksheetDto
): Promise<{ status: 'success' | 'error'; message?: string; data?: any }> {
  try {
    // For duplication, we use the add question API with the duplicated data
    const response = await addQuestionToWorksheet(worksheetId, questionData);

    if (response.status === 'success') {
      // Revalidate the worksheet review page to reflect changes
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      revalidatePath(ERoutes.MANAGE_WORKSHEET);

      return {
        status: 'success',
        message: response.data?.message || 'Question duplicated successfully.',
        data: response.data
      };
    } else {
      return {
        status: 'error',
        message: response.message || 'Failed to duplicate question.'
      };
    }
  } catch (error: any) {
    console.error('Error in handleDuplicateQuestionAction:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while duplicating the question.'
    };
  }
}

/**
 * Server Action to bulk delete questions from a worksheet
 */
export async function handleBulkDeleteQuestionsAction(
  worksheetId: string,
  questionIds: string[],
  reason?: string,
  forceRemoval?: boolean
): Promise<{ status: 'success' | 'error'; message?: string; data?: any }> {
  try {
    const bulkData: IBulkRemoveQuestionsDto = {
      questionIds,
      reason,
      forceRemoval
    };

    const response = await bulkRemoveQuestionsFromWorksheet(worksheetId, bulkData);

    if (response.status === 'success') {
      // Revalidate the worksheet review page to reflect changes
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      revalidatePath(ERoutes.MANAGE_WORKSHEET);

      return {
        status: 'success',
        message: `Successfully deleted ${response.data?.successCount || questionIds.length} questions.`,
        data: response.data
      };
    } else {
      return {
        status: 'error',
        message: response.message || 'Failed to delete questions.'
      };
    }
  } catch (error: any) {
    console.error('Error in handleBulkDeleteQuestionsAction:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while deleting questions.'
    };
  }
}

/**
 * Server Action to bulk add questions to a worksheet
 */
export async function handleBulkAddQuestionsAction(
  worksheetId: string,
  questions: IAddQuestionToWorksheetDto[],
  insertPosition?: number,
  validateQuestions?: boolean,
  reason?: string
): Promise<{ status: 'success' | 'error'; message?: string; data?: any }> {
  try {
    const bulkData: IBulkAddQuestionsDto = {
      questions,
      insertPosition,
      validateQuestions,
      reason
    };

    const response = await bulkAddQuestionsToWorksheet(worksheetId, bulkData);

    if (response.status === 'success') {
      // Revalidate the worksheet review page to reflect changes
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      revalidatePath(ERoutes.MANAGE_WORKSHEET);

      return {
        status: 'success',
        message: `Successfully added ${response.data?.successCount || questions.length} questions.`,
        data: response.data
      };
    } else {
      return {
        status: 'error',
        message: response.message || 'Failed to add questions.'
      };
    }
  } catch (error: any) {
    console.error('Error in handleBulkAddQuestionsAction:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while adding questions.'
    };
  }
}

/**
 * Server Action to bulk update questions in a worksheet
 */
export async function handleBulkUpdateQuestionsAction(
  worksheetId: string,
  updates: { questionId: string; updates: IUpdateWorksheetQuestionDto }[],
  reason?: string,
  validateQuestions?: boolean
): Promise<{ status: 'success' | 'error'; message?: string; data?: any }> {
  try {
    const bulkData: IBulkUpdateQuestionsDto = {
      updates,
      reason,
      validateQuestions
    };

    const response = await bulkUpdateQuestionsInWorksheet(worksheetId, bulkData);

    if (response.status === 'success') {
      // Revalidate the worksheet review page to reflect changes
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      revalidatePath(ERoutes.MANAGE_WORKSHEET);

      return {
        status: 'success',
        message: `Successfully updated ${response.data?.successCount || updates.length} questions.`,
        data: response.data
      };
    } else {
      return {
        status: 'error',
        message: response.message || 'Failed to update questions.'
      };
    }
  } catch (error: any) {
    console.error('Error in handleBulkUpdateQuestionsAction:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while updating questions.'
    };
  }
}
