{"name": "edu-sg", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format-check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-slot": "^1.2.0", "@react-pdf/renderer": "^4.3.0", "@tailwindcss/postcss": "^4.1.4", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "framer-motion": "10.16.4", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.488.0", "next": "15.3.0", "next-auth": "^4.24.11", "quill": "^2.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-icons": "^5.5.0", "react-inlinesvg": "^4.2.0", "react-pdf": "^9.2.1", "react-quill": "^2.0.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.5", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "daisyui": "^5.0.25", "eslint": "^9", "eslint-config-next": "15.3.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "typescript": "^5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}