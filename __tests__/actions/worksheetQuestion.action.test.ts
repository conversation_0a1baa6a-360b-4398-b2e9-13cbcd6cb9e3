import {
  handleCreateQuestionAction,
  handleUpdateQuestionAction,
  handleDeleteQuestionAction,
  handleReorderQuestionsAction,
  handleDuplicateQuestionAction,
  handleBulkDeleteQuestionsAction,
} from '@/actions/worksheetQuestion.action';

// Mock the API functions
jest.mock('@/apis/worksheetQuestionApi', () => ({
  addQuestionToWorksheet: jest.fn(),
  updateQuestionInWorksheet: jest.fn(),
  removeQuestionFromWorksheet: jest.fn(),
  reorderQuestionsInWorksheet: jest.fn(),
  bulkRemoveQuestionsFromWorksheet: jest.fn(),
}));

// Mock revalidatePath
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}));

import {
  addQuestionToWorksheet,
  updateQuestionInWorksheet,
  removeQuestionFromWorksheet,
  reorderQuestionsInWorksheet,
  bulkRemoveQuestionsFromWorksheet,
} from '@/apis/worksheetQuestionApi';

const mockAddQuestion = addQuestionToWorksheet as jest.MockedFunction<typeof addQuestionToWorksheet>;
const mockUpdateQuestion = updateQuestionInWorksheet as jest.MockedFunction<typeof updateQuestionInWorksheet>;
const mockRemoveQuestion = removeQuestionFromWorksheet as jest.MockedFunction<typeof removeQuestionFromWorksheet>;
const mockReorderQuestions = reorderQuestionsInWorksheet as jest.MockedFunction<typeof reorderQuestionsInWorksheet>;
const mockBulkRemoveQuestions = bulkRemoveQuestionsFromWorksheet as jest.MockedFunction<typeof bulkRemoveQuestionsFromWorksheet>;

describe('Worksheet Question Actions', () => {
  const mockWorksheetId = 'test-worksheet-id';
  const mockQuestionId = 'test-question-id';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('handleCreateQuestionAction', () => {
    const mockQuestionData = {
      type: 'MULTIPLE_CHOICE' as const,
      content: 'Test question content',
      options: ['Option A', 'Option B', 'Option C', 'Option D'],
      answer: ['Option A'],
      explain: 'This is the correct answer because...',
      points: 1,
      difficulty: 'MEDIUM' as const,
    };

    it('should create a question successfully', async () => {
      const mockResponse = {
        status: 'success' as const,
        data: {
          questionId: 'new-question-id',
          position: 1,
          worksheetId: mockWorksheetId,
          totalQuestions: 1,
          message: 'Question created successfully',
        },
      };

      mockAddQuestion.mockResolvedValue(mockResponse);

      const result = await handleCreateQuestionAction(mockWorksheetId, mockQuestionData);

      expect(mockAddQuestion).toHaveBeenCalledWith(mockWorksheetId, mockQuestionData);
      expect(result).toEqual({
        status: 'success',
        message: 'Question created successfully',
        data: mockResponse.data,
      });
    });

    it('should handle creation failure', async () => {
      const mockResponse = {
        status: 'error' as const,
        message: 'Failed to create question',
      };

      mockAddQuestion.mockResolvedValue(mockResponse);

      const result = await handleCreateQuestionAction(mockWorksheetId, mockQuestionData);

      expect(result).toEqual({
        status: 'error',
        message: 'Failed to create question',
      });
    });

    it('should handle unexpected errors', async () => {
      mockAddQuestion.mockRejectedValue(new Error('Network error'));

      const result = await handleCreateQuestionAction(mockWorksheetId, mockQuestionData);

      expect(result).toEqual({
        status: 'error',
        message: 'Network error',
      });
    });
  });

  describe('handleUpdateQuestionAction', () => {
    const mockUpdateData = {
      content: 'Updated question content',
      points: 2,
    };

    it('should update a question successfully', async () => {
      const mockResponse = {
        status: 'success' as const,
        data: {
          questionId: mockQuestionId,
          version: 2,
          updatedFields: ['content', 'points'],
          worksheetId: mockWorksheetId,
          message: 'Question updated successfully',
        },
      };

      mockUpdateQuestion.mockResolvedValue(mockResponse);

      const result = await handleUpdateQuestionAction(mockWorksheetId, mockQuestionId, mockUpdateData);

      expect(mockUpdateQuestion).toHaveBeenCalledWith(mockWorksheetId, mockQuestionId, mockUpdateData);
      expect(result).toEqual({
        status: 'success',
        message: 'Question updated successfully',
        data: mockResponse.data,
      });
    });
  });

  describe('handleDeleteQuestionAction', () => {
    it('should delete a question successfully', async () => {
      const mockResponse = {
        status: 'success' as const,
        data: {
          removedQuestionId: mockQuestionId,
          worksheetId: mockWorksheetId,
          totalQuestions: 0,
          positionsUpdated: [],
          message: 'Question deleted successfully',
        },
      };

      mockRemoveQuestion.mockResolvedValue(mockResponse);

      const result = await handleDeleteQuestionAction(mockWorksheetId, mockQuestionId);

      expect(mockRemoveQuestion).toHaveBeenCalledWith(mockWorksheetId, mockQuestionId, undefined);
      expect(result).toEqual({
        status: 'success',
        message: 'Question deleted successfully',
        data: mockResponse.data,
      });
    });

    it('should delete a question with reason', async () => {
      const reason = 'Question is outdated';
      const mockResponse = {
        status: 'success' as const,
        data: {
          removedQuestionId: mockQuestionId,
          worksheetId: mockWorksheetId,
          totalQuestions: 0,
          positionsUpdated: [],
          message: 'Question deleted successfully',
        },
      };

      mockRemoveQuestion.mockResolvedValue(mockResponse);

      const result = await handleDeleteQuestionAction(mockWorksheetId, mockQuestionId, reason);

      expect(mockRemoveQuestion).toHaveBeenCalledWith(mockWorksheetId, mockQuestionId, reason);
      expect(result.status).toBe('success');
    });
  });

  describe('handleReorderQuestionsAction', () => {
    it('should reorder questions successfully', async () => {
      const reorderData = {
        questionId: mockQuestionId,
        newPosition: 2,
      };

      const mockResponse = {
        status: 'success' as const,
        data: {
          worksheetId: mockWorksheetId,
          reorderedQuestions: [
            {
              questionId: mockQuestionId,
              oldPosition: 1,
              newPosition: 2,
            },
          ],
          totalQuestions: 2,
          message: 'Questions reordered successfully',
        },
      };

      mockReorderQuestions.mockResolvedValue(mockResponse);

      const result = await handleReorderQuestionsAction(mockWorksheetId, reorderData);

      expect(mockReorderQuestions).toHaveBeenCalledWith(mockWorksheetId, reorderData);
      expect(result).toEqual({
        status: 'success',
        message: 'Questions reordered successfully',
        data: mockResponse.data,
      });
    });
  });

  describe('handleBulkDeleteQuestionsAction', () => {
    it('should bulk delete questions successfully', async () => {
      const questionIds = ['question-1', 'question-2', 'question-3'];
      const mockResponse = {
        status: 'success' as const,
        data: {
          success: true,
          successCount: 3,
          failureCount: 0,
          totalCount: 3,
          data: {
            removedQuestionIds: questionIds,
            positionsUpdated: [],
            totalQuestions: 0,
          },
        },
      };

      mockBulkRemoveQuestions.mockResolvedValue(mockResponse);

      const result = await handleBulkDeleteQuestionsAction(mockWorksheetId, questionIds);

      expect(mockBulkRemoveQuestions).toHaveBeenCalledWith(mockWorksheetId, {
        questionIds,
        reason: undefined,
        forceRemoval: undefined,
      });
      expect(result).toEqual({
        status: 'success',
        message: 'Successfully deleted 3 questions.',
        data: mockResponse.data,
      });
    });

    it('should handle bulk delete with reason and force removal', async () => {
      const questionIds = ['question-1', 'question-2'];
      const reason = 'Bulk cleanup';
      const forceRemoval = true;

      const mockResponse = {
        status: 'success' as const,
        data: {
          success: true,
          successCount: 2,
          failureCount: 0,
          totalCount: 2,
        },
      };

      mockBulkRemoveQuestions.mockResolvedValue(mockResponse);

      const result = await handleBulkDeleteQuestionsAction(mockWorksheetId, questionIds, reason, forceRemoval);

      expect(mockBulkRemoveQuestions).toHaveBeenCalledWith(mockWorksheetId, {
        questionIds,
        reason,
        forceRemoval,
      });
      expect(result.status).toBe('success');
    });
  });

  describe('handleDuplicateQuestionAction', () => {
    it('should duplicate a question successfully', async () => {
      const mockQuestionData = {
        type: 'MULTIPLE_CHOICE' as const,
        content: 'Duplicated question content',
        options: ['Option A', 'Option B'],
        answer: ['Option A'],
        explain: 'Explanation for duplicated question',
        points: 1,
        difficulty: 'EASY' as const,
      };

      const mockResponse = {
        status: 'success' as const,
        data: {
          questionId: 'duplicated-question-id',
          position: 2,
          worksheetId: mockWorksheetId,
          totalQuestions: 2,
          message: 'Question duplicated successfully',
        },
      };

      mockAddQuestion.mockResolvedValue(mockResponse);

      const result = await handleDuplicateQuestionAction(mockWorksheetId, mockQuestionId, mockQuestionData);

      expect(mockAddQuestion).toHaveBeenCalledWith(mockWorksheetId, mockQuestionData);
      expect(result).toEqual({
        status: 'success',
        message: 'Question duplicated successfully',
        data: mockResponse.data,
      });
    });
  });
});
