import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { WorksheetQuestionManager } from '@/components/organisms/WorksheetQuestionManager/WorksheetQuestionManager';
import { IWorksheetQuestion } from '@/apis/worksheetQuestionApi';

// Mock the server actions
jest.mock('@/actions/worksheetQuestion.action', () => ({
  handleCreateQuestionAction: jest.fn(),
  handleUpdateQuestionAction: jest.fn(),
  handleDeleteQuestionAction: jest.fn(),
  handleReorderQuestionsAction: jest.fn(),
  handleBulkDeleteQuestionsAction: jest.fn(),
}));

// Mock drag and drop
jest.mock('@hello-pangea/dnd', () => ({
  DragDropContext: ({ children }: any) => children,
  Droppable: ({ children }: any) => children({ innerRef: jest.fn(), droppableProps: {}, placeholder: null }, {}),
  Draggable: ({ children }: any) => children({ innerRef: jest.fn(), draggableProps: {}, dragHandleProps: {} }, {}),
}));

const mockQuestions: IWorksheetQuestion[] = [
  {
    id: 'q1',
    type: 'MULTIPLE_CHOICE',
    content: 'What is 2 + 2?',
    options: ['2', '3', '4', '5'],
    answer: ['4'],
    explain: 'Basic arithmetic',
    position: 1,
    points: 1,
    difficulty: 'EASY',
    createdAt: new Date(),
    updatedAt: new Date(),
    version: 1,
  },
  {
    id: 'q2',
    type: 'TRUE_FALSE',
    content: 'The sky is blue.',
    options: ['True', 'False'],
    answer: ['True'],
    explain: 'The sky appears blue due to light scattering',
    position: 2,
    points: 1,
    difficulty: 'EASY',
    createdAt: new Date(),
    updatedAt: new Date(),
    version: 1,
  },
];

describe('WorksheetQuestionManager', () => {
  const defaultProps = {
    worksheetId: 'test-worksheet-id',
    initialQuestions: mockQuestions,
    userRole: 'teacher',
    userSchoolId: 'test-school-id',
    isReadOnly: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the question manager with questions', () => {
    render(<WorksheetQuestionManager {...defaultProps} />);

    expect(screen.getByText('Questions (2)')).toBeInTheDocument();
    expect(screen.getByText('What is 2 + 2?')).toBeInTheDocument();
    expect(screen.getByText('The sky is blue.')).toBeInTheDocument();
  });

  it('shows add question button for authorized users', () => {
    render(<WorksheetQuestionManager {...defaultProps} />);

    expect(screen.getByRole('button', { name: /add question/i })).toBeInTheDocument();
  });

  it('hides add question button for unauthorized users', () => {
    render(<WorksheetQuestionManager {...defaultProps} userRole="student" />);

    expect(screen.queryByRole('button', { name: /add question/i })).not.toBeInTheDocument();
  });

  it('shows bulk operations for admin users', () => {
    render(<WorksheetQuestionManager {...defaultProps} userRole="admin" />);

    expect(screen.getByRole('button', { name: /select all/i })).toBeInTheDocument();
  });

  it('hides bulk operations for regular teachers', () => {
    render(<WorksheetQuestionManager {...defaultProps} userRole="teacher" />);

    expect(screen.queryByRole('button', { name: /select all/i })).not.toBeInTheDocument();
  });

  it('opens add question modal when add button is clicked', async () => {
    const user = userEvent.setup();
    render(<WorksheetQuestionManager {...defaultProps} />);

    const addButton = screen.getByRole('button', { name: /add question/i });
    await user.click(addButton);

    expect(screen.getByText('Add New Question')).toBeInTheDocument();
  });

  it('displays question types correctly', () => {
    render(<WorksheetQuestionManager {...defaultProps} />);

    expect(screen.getByText('Multiple Choice')).toBeInTheDocument();
    expect(screen.getByText('True/False')).toBeInTheDocument();
  });

  it('displays question difficulty levels', () => {
    render(<WorksheetQuestionManager {...defaultProps} />);

    const easyLabels = screen.getAllByText('EASY');
    expect(easyLabels).toHaveLength(2);
  });

  it('shows edit and delete buttons for authorized users', () => {
    render(<WorksheetQuestionManager {...defaultProps} />);

    const editButtons = screen.getAllByLabelText(/edit question/i);
    const deleteButtons = screen.getAllByLabelText(/delete question/i);

    expect(editButtons).toHaveLength(2);
    expect(deleteButtons).toHaveLength(2);
  });

  it('handles question selection for bulk operations', async () => {
    const user = userEvent.setup();
    render(<WorksheetQuestionManager {...defaultProps} userRole="admin" />);

    const selectAllButton = screen.getByRole('button', { name: /select all/i });
    await user.click(selectAllButton);

    expect(screen.getByText('2 of 2 selected')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /delete \(2\)/i })).toBeInTheDocument();
  });

  it('opens delete modal when delete button is clicked', async () => {
    const user = userEvent.setup();
    render(<WorksheetQuestionManager {...defaultProps} />);

    const deleteButtons = screen.getAllByLabelText(/delete question/i);
    await user.click(deleteButtons[0]);

    expect(screen.getByText('Delete Question')).toBeInTheDocument();
    expect(screen.getByText('Are you sure?')).toBeInTheDocument();
  });

  it('opens edit modal when edit button is clicked', async () => {
    const user = userEvent.setup();
    render(<WorksheetQuestionManager {...defaultProps} />);

    const editButtons = screen.getAllByLabelText(/edit question/i);
    await user.click(editButtons[0]);

    expect(screen.getByText('Edit Question')).toBeInTheDocument();
  });

  it('displays empty state when no questions exist', () => {
    render(<WorksheetQuestionManager {...defaultProps} initialQuestions={[]} />);

    expect(screen.getByText('No questions yet')).toBeInTheDocument();
    expect(screen.getByText(/start building your worksheet/i)).toBeInTheDocument();
  });

  it('respects read-only mode', () => {
    render(<WorksheetQuestionManager {...defaultProps} isReadOnly={true} />);

    expect(screen.queryByRole('button', { name: /add question/i })).not.toBeInTheDocument();
    expect(screen.queryByLabelText(/edit question/i)).not.toBeInTheDocument();
    expect(screen.queryByLabelText(/delete question/i)).not.toBeInTheDocument();
  });

  it('shows question options preview', () => {
    render(<WorksheetQuestionManager {...defaultProps} />);

    expect(screen.getByText('Options:')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('4')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
  });

  it('highlights correct answers in options', () => {
    render(<WorksheetQuestionManager {...defaultProps} />);

    // The correct answer "4" should have a different styling (badge-success class)
    const correctAnswerBadge = screen.getByText('4');
    expect(correctAnswerBadge.closest('.badge')).toHaveClass('badge-success');
  });

  it('displays question metadata', () => {
    render(<WorksheetQuestionManager {...defaultProps} />);

    const pointLabels = screen.getAllByText(/1 point/);
    expect(pointLabels).toHaveLength(2);

    const positionLabels = screen.getAllByText(/position: /i);
    expect(positionLabels).toHaveLength(2);
  });

  it('handles loading states', () => {
    render(<WorksheetQuestionManager {...defaultProps} />);

    // Initially, loading should be false
    const addButton = screen.getByRole('button', { name: /add question/i });
    expect(addButton).not.toBeDisabled();
  });

  it('shows question numbers correctly', () => {
    render(<WorksheetQuestionManager {...defaultProps} />);

    expect(screen.getByText('Q1')).toBeInTheDocument();
    expect(screen.getByText('Q2')).toBeInTheDocument();
  });

  it('truncates long question content', () => {
    const longQuestion: IWorksheetQuestion = {
      ...mockQuestions[0],
      content: 'This is a very long question content that should be truncated when displayed in the question list to maintain a clean and readable interface for users.',
    };

    render(<WorksheetQuestionManager {...defaultProps} initialQuestions={[longQuestion]} />);

    // The content should be displayed but potentially truncated with CSS
    expect(screen.getByText(/this is a very long question content/i)).toBeInTheDocument();
  });
});
