import {
  addQuestionToWorksheet,
  removeQuestionFromWorksheet,
  updateQuestionInWorksheet,
  replaceQuestionInWorksheet,
  reorderQuestionsInWorksheet,
  bulkAddQuestionsToWorksheet,
  bulkRemoveQuestionsFromWorksheet,
  bulkUpdateQuestionsInWorksheet,
} from '@/apis/worksheetQuestionApi';

// Mock the request function
jest.mock('@/apis/request', () => ({
  request: jest.fn(),
}));

import { request } from '@/apis/request';
const mockRequest = request as jest.MockedFunction<typeof request>;

describe('Worksheet Question API', () => {
  const mockWorksheetId = 'test-worksheet-id';
  const mockQuestionId = 'test-question-id';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('addQuestionToWorksheet', () => {
    it('should add a question successfully', async () => {
      const questionData = {
        type: 'MULTIPLE_CHOICE' as const,
        content: 'Test question',
        options: ['A', 'B', 'C', 'D'],
        answer: ['A'],
        explain: 'Explanation',
        points: 1,
        difficulty: 'MEDIUM' as const,
      };

      const mockResponse = {
        status: 'success' as const,
        data: {
          questionId: 'new-question-id',
          position: 1,
          worksheetId: mockWorksheetId,
          totalQuestions: 1,
          message: 'Question added successfully',
        },
      };

      mockRequest.mockResolvedValue(mockResponse);

      const result = await addQuestionToWorksheet(mockWorksheetId, questionData);

      expect(mockRequest).toHaveBeenCalledWith({
        url: `/worksheets/${mockWorksheetId}/questions`,
        options: {
          method: 'POST',
          body: JSON.stringify(questionData),
        },
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors', async () => {
      const questionData = {
        type: 'MULTIPLE_CHOICE' as const,
        content: 'Test question',
        options: ['A', 'B'],
        answer: ['A'],
        explain: 'Explanation',
      };

      mockRequest.mockRejectedValue(new Error('API Error'));

      const result = await addQuestionToWorksheet(mockWorksheetId, questionData);

      expect(result).toEqual({
        status: 'error',
        message: 'API Error',
      });
    });
  });

  describe('removeQuestionFromWorksheet', () => {
    it('should remove a question successfully', async () => {
      const mockResponse = {
        status: 'success' as const,
        data: {
          removedQuestionId: mockQuestionId,
          worksheetId: mockWorksheetId,
          totalQuestions: 0,
          positionsUpdated: [],
          message: 'Question removed successfully',
        },
      };

      mockRequest.mockResolvedValue(mockResponse);

      const result = await removeQuestionFromWorksheet(mockWorksheetId, mockQuestionId);

      expect(mockRequest).toHaveBeenCalledWith({
        url: `/worksheets/${mockWorksheetId}/questions/${mockQuestionId}`,
        options: {
          method: 'DELETE',
        },
      });
      expect(result).toEqual(mockResponse);
    });

    it('should include reason in URL when provided', async () => {
      const reason = 'Question is outdated';
      const mockResponse = {
        status: 'success' as const,
        data: {
          removedQuestionId: mockQuestionId,
          worksheetId: mockWorksheetId,
          totalQuestions: 0,
          positionsUpdated: [],
          message: 'Question removed successfully',
        },
      };

      mockRequest.mockResolvedValue(mockResponse);

      await removeQuestionFromWorksheet(mockWorksheetId, mockQuestionId, reason);

      expect(mockRequest).toHaveBeenCalledWith({
        url: `/worksheets/${mockWorksheetId}/questions/${mockQuestionId}?reason=${encodeURIComponent(reason)}`,
        options: {
          method: 'DELETE',
        },
      });
    });
  });

  describe('updateQuestionInWorksheet', () => {
    it('should update a question successfully', async () => {
      const updateData = {
        content: 'Updated question content',
        points: 2,
      };

      const mockResponse = {
        status: 'success' as const,
        data: {
          questionId: mockQuestionId,
          version: 2,
          updatedFields: ['content', 'points'],
          worksheetId: mockWorksheetId,
          message: 'Question updated successfully',
        },
      };

      mockRequest.mockResolvedValue(mockResponse);

      const result = await updateQuestionInWorksheet(mockWorksheetId, mockQuestionId, updateData);

      expect(mockRequest).toHaveBeenCalledWith({
        url: `/worksheets/${mockWorksheetId}/questions/${mockQuestionId}`,
        options: {
          method: 'PATCH',
          body: JSON.stringify(updateData),
        },
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('replaceQuestionInWorksheet', () => {
    it('should replace a question successfully', async () => {
      const questionData = {
        type: 'TRUE_FALSE' as const,
        content: 'Replacement question',
        options: ['True', 'False'],
        answer: ['True'],
        explain: 'New explanation',
        version: 1,
      };

      const mockResponse = {
        status: 'success' as const,
        data: {
          questionId: mockQuestionId,
          version: 2,
          worksheetId: mockWorksheetId,
          message: 'Question replaced successfully',
        },
      };

      mockRequest.mockResolvedValue(mockResponse);

      const result = await replaceQuestionInWorksheet(mockWorksheetId, mockQuestionId, questionData);

      expect(mockRequest).toHaveBeenCalledWith({
        url: `/worksheets/${mockWorksheetId}/questions/${mockQuestionId}`,
        options: {
          method: 'PUT',
          body: JSON.stringify(questionData),
        },
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('reorderQuestionsInWorksheet', () => {
    it('should reorder questions successfully', async () => {
      const reorderData = {
        questionId: mockQuestionId,
        newPosition: 3,
      };

      const mockResponse = {
        status: 'success' as const,
        data: {
          worksheetId: mockWorksheetId,
          reorderedQuestions: [
            {
              questionId: mockQuestionId,
              oldPosition: 1,
              newPosition: 3,
            },
          ],
          totalQuestions: 3,
          message: 'Questions reordered successfully',
        },
      };

      mockRequest.mockResolvedValue(mockResponse);

      const result = await reorderQuestionsInWorksheet(mockWorksheetId, reorderData);

      expect(mockRequest).toHaveBeenCalledWith({
        url: `/worksheets/${mockWorksheetId}/questions/reorder`,
        options: {
          method: 'PATCH',
          body: JSON.stringify(reorderData),
        },
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('bulkAddQuestionsToWorksheet', () => {
    it('should bulk add questions successfully', async () => {
      const bulkData = {
        questions: [
          {
            type: 'MULTIPLE_CHOICE' as const,
            content: 'Question 1',
            options: ['A', 'B'],
            answer: ['A'],
            explain: 'Explanation 1',
          },
          {
            type: 'TRUE_FALSE' as const,
            content: 'Question 2',
            options: ['True', 'False'],
            answer: ['True'],
            explain: 'Explanation 2',
          },
        ],
        insertPosition: 1,
        validateQuestions: true,
        reason: 'Bulk import',
      };

      const mockResponse = {
        status: 'success' as const,
        data: {
          success: true,
          successCount: 2,
          failureCount: 0,
          totalCount: 2,
          successes: [
            { questionId: 'q1', position: 1, index: 0 },
            { questionId: 'q2', position: 2, index: 1 },
          ],
          failures: [],
          timestamp: '2023-01-01T00:00:00Z',
          processingTimeMs: 100,
        },
      };

      mockRequest.mockResolvedValue(mockResponse);

      const result = await bulkAddQuestionsToWorksheet(mockWorksheetId, bulkData);

      expect(mockRequest).toHaveBeenCalledWith({
        url: `/worksheets/${mockWorksheetId}/questions/bulk`,
        options: {
          method: 'POST',
          body: JSON.stringify(bulkData),
        },
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('bulkRemoveQuestionsFromWorksheet', () => {
    it('should bulk remove questions successfully', async () => {
      const bulkData = {
        questionIds: ['q1', 'q2', 'q3'],
        reason: 'Cleanup',
        forceRemoval: false,
      };

      const mockResponse = {
        status: 'success' as const,
        data: {
          success: true,
          successCount: 3,
          failureCount: 0,
          totalCount: 3,
          data: {
            removedQuestionIds: ['q1', 'q2', 'q3'],
            positionsUpdated: [],
            totalQuestions: 0,
          },
          timestamp: '2023-01-01T00:00:00Z',
          processingTimeMs: 150,
        },
      };

      mockRequest.mockResolvedValue(mockResponse);

      const result = await bulkRemoveQuestionsFromWorksheet(mockWorksheetId, bulkData);

      expect(mockRequest).toHaveBeenCalledWith({
        url: `/worksheets/${mockWorksheetId}/questions/bulk`,
        options: {
          method: 'DELETE',
          body: JSON.stringify(bulkData),
        },
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('bulkUpdateQuestionsInWorksheet', () => {
    it('should bulk update questions successfully', async () => {
      const bulkData = {
        updates: [
          {
            questionId: 'q1',
            updates: { points: 2 },
          },
          {
            questionId: 'q2',
            updates: { difficulty: 'HARD' as const },
          },
        ],
        reason: 'Point adjustment',
        validateQuestions: true,
      };

      const mockResponse = {
        status: 'success' as const,
        data: {
          success: true,
          successCount: 2,
          failureCount: 0,
          totalCount: 2,
          data: {
            updatedQuestions: [
              { questionId: 'q1', version: 2, updatedFields: ['points'] },
              { questionId: 'q2', version: 2, updatedFields: ['difficulty'] },
            ],
          },
          timestamp: '2023-01-01T00:00:00Z',
          processingTimeMs: 120,
        },
      };

      mockRequest.mockResolvedValue(mockResponse);

      const result = await bulkUpdateQuestionsInWorksheet(mockWorksheetId, bulkData);

      expect(mockRequest).toHaveBeenCalledWith({
        url: `/worksheets/${mockWorksheetId}/questions/bulk`,
        options: {
          method: 'PATCH',
          body: JSON.stringify(bulkData),
        },
      });
      expect(result).toEqual(mockResponse);
    });
  });
});
