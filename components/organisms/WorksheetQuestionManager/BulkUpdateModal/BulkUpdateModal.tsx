'use client';

import React, { useState, useRef, useEffect } from 'react';
import { X, Loader2, Edit } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { IWorksheetQuestion, IUpdateWorksheetQuestionDto } from '@/apis/worksheetQuestionApi';
import { handleBulkUpdateQuestionsAction } from '@/actions/worksheetQuestion.action';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';

export interface BulkUpdateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (updatedCount: number) => void;
  worksheetId: string;
  selectedQuestions: IWorksheetQuestion[];
  className?: string;
}

interface BulkUpdateFormData {
  updateFields: {
    difficulty?: string;
    subject?: string;
    points?: number;
    status?: string;
  };
  reason: string;
  validateQuestions: boolean;
}

export const BulkUpdateModal: React.FC<BulkUpdateModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  worksheetId,
  selectedQuestions,
  className
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFields, setSelectedFields] = useState<Set<string>>(new Set());
  const dialogRef = useRef<HTMLDialogElement>(null);

  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors }
  } = useForm<BulkUpdateFormData>({
    defaultValues: {
      updateFields: {},
      reason: 'Bulk update questions',
      validateQuestions: true
    }
  });

  // Handle dialog open/close
  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    if (isOpen) {
      dialog.showModal();
    } else {
      dialog.close();
    }
  }, [isOpen]);

  const onSubmit = async (data: BulkUpdateFormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Build update data only for selected fields
      const updateData: IUpdateWorksheetQuestionDto = {};
      
      if (selectedFields.has('difficulty') && data.updateFields.difficulty) {
        updateData.difficulty = data.updateFields.difficulty as any;
      }
      if (selectedFields.has('subject') && data.updateFields.subject) {
        updateData.subject = data.updateFields.subject;
      }
      if (selectedFields.has('points') && data.updateFields.points !== undefined) {
        updateData.points = data.updateFields.points;
      }
      if (selectedFields.has('status') && data.updateFields.status) {
        updateData.status = data.updateFields.status as any;
      }

      if (Object.keys(updateData).length === 0) {
        setError('Please select at least one field to update');
        return;
      }

      const updates = selectedQuestions.map(question => ({
        questionId: question.id,
        updates: { ...updateData, version: question.version }
      }));

      const response = await handleBulkUpdateQuestionsAction(
        worksheetId,
        updates,
        data.reason,
        data.validateQuestions
      );

      if (response.status === 'success') {
        onSuccess(response.data?.successCount || selectedQuestions.length);
        handleClose();
      } else {
        setError(response.message || 'Failed to update questions');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleField = (field: string) => {
    const newSelectedFields = new Set(selectedFields);
    if (newSelectedFields.has(field)) {
      newSelectedFields.delete(field);
    } else {
      newSelectedFields.add(field);
    }
    setSelectedFields(newSelectedFields);
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setError(null);
      setSelectedFields(new Set());
      reset();
      onClose();
    }
  };

  return (
    <dialog
      ref={dialogRef}
      className={cn("modal modal-bottom sm:modal-middle", className)}
      onClose={handleClose}
    >
      <div className="modal-box w-full !max-w-[800px] max-h-[95vh] overflow-y-auto p-3 sm:p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-3 sm:mb-4">
          <h3 className="text-base sm:text-lg font-semibold">
            Bulk Update Questions ({selectedQuestions.length})
          </h3>
          <form method="dialog">
            <button
              type="button"
              onClick={handleClose}
              className="btn btn-ghost btn-circle btn-sm sm:btn-md"
              disabled={isSubmitting}
            >
              <X className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500" />
            </button>
          </form>
        </div>

        {/* Error Message */}
        {error && (
          <div className="alert alert-error mb-3">
            <span>{error}</span>
          </div>
        )}

        {/* Selected Questions Preview */}
        <div className="bg-base-50 p-3 rounded-lg mb-4">
          <h4 className="font-medium mb-2">Selected Questions:</h4>
          <div className="text-sm text-base-content/70 max-h-32 overflow-y-auto">
            {selectedQuestions.map((question, index) => (
              <div key={question.id} className="truncate">
                {index + 1}. {question.content.replace(/<[^>]*>/g, '').substring(0, 60)}...
              </div>
            ))}
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Update Fields */}
          <div className="space-y-4">
            <h4 className="font-medium">Select fields to update:</h4>

            {/* Difficulty */}
            <div className="form-control">
              <label className="label cursor-pointer">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={selectedFields.has('difficulty')}
                    onChange={() => toggleField('difficulty')}
                    className="checkbox checkbox-primary checkbox-sm"
                    disabled={isSubmitting}
                  />
                  <span className="label-text font-medium">Difficulty</span>
                </div>
              </label>
              {selectedFields.has('difficulty') && (
                <select
                  {...register('updateFields.difficulty')}
                  className="select select-bordered select-sm mt-2"
                  disabled={isSubmitting}
                >
                  <option value="">Select difficulty</option>
                  <option value="EASY">🟢 Easy</option>
                  <option value="MEDIUM">🟡 Medium</option>
                  <option value="HARD">🔴 Hard</option>
                </select>
              )}
            </div>

            {/* Subject */}
            <div className="form-control">
              <label className="label cursor-pointer">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={selectedFields.has('subject')}
                    onChange={() => toggleField('subject')}
                    className="checkbox checkbox-primary checkbox-sm"
                    disabled={isSubmitting}
                  />
                  <span className="label-text font-medium">Subject</span>
                </div>
              </label>
              {selectedFields.has('subject') && (
                <input
                  {...register('updateFields.subject')}
                  className="input input-bordered input-sm mt-2"
                  placeholder="e.g., Mathematics"
                  disabled={isSubmitting}
                />
              )}
            </div>

            {/* Points */}
            <div className="form-control">
              <label className="label cursor-pointer">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={selectedFields.has('points')}
                    onChange={() => toggleField('points')}
                    className="checkbox checkbox-primary checkbox-sm"
                    disabled={isSubmitting}
                  />
                  <span className="label-text font-medium">Points</span>
                </div>
              </label>
              {selectedFields.has('points') && (
                <input
                  type="number"
                  {...register('updateFields.points', { min: 0, max: 100 })}
                  className="input input-bordered input-sm mt-2"
                  min="0"
                  max="100"
                  placeholder="Points"
                  disabled={isSubmitting}
                />
              )}
            </div>

            {/* Status */}
            <div className="form-control">
              <label className="label cursor-pointer">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={selectedFields.has('status')}
                    onChange={() => toggleField('status')}
                    className="checkbox checkbox-primary checkbox-sm"
                    disabled={isSubmitting}
                  />
                  <span className="label-text font-medium">Status</span>
                </div>
              </label>
              {selectedFields.has('status') && (
                <select
                  {...register('updateFields.status')}
                  className="select select-bordered select-sm mt-2"
                  disabled={isSubmitting}
                >
                  <option value="">Select status</option>
                  <option value="ACTIVE">Active</option>
                  <option value="INACTIVE">Inactive</option>
                  <option value="DRAFT">Draft</option>
                  <option value="ARCHIVED">Archived</option>
                </select>
              )}
            </div>
          </div>

          {/* Bulk Options */}
          <div className="bg-base-50 p-4 rounded-lg space-y-3">
            <h4 className="font-medium">Update Options</h4>
            
            <div className="form-control">
              <label className="label cursor-pointer">
                <span className="label-text">Validate Questions</span>
                <input
                  type="checkbox"
                  {...register('validateQuestions')}
                  className="checkbox checkbox-primary"
                  disabled={isSubmitting}
                />
              </label>
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Reason</span>
              </label>
              <input
                {...register('reason')}
                className="input input-bordered input-sm"
                placeholder="Reason for bulk update..."
                disabled={isSubmitting}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row justify-end gap-2 sm:gap-3 pt-4 border-t border-base-200">
            <Button
              type="button"
              variant="ghost"
              onClick={handleClose}
              disabled={isSubmitting}
              className="btn-sm w-full sm:w-auto"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={isSubmitting || selectedFields.size === 0}
              className="btn-primary btn-sm w-full sm:w-auto"
            >
              {isSubmitting && <Loader2 className="w-4 h-4 animate-spin mr-2" />}
              Update {selectedQuestions.length} Question{selectedQuestions.length !== 1 ? 's' : ''}
            </Button>
          </div>
        </form>
      </div>
      <form method="dialog" className="modal-backdrop">
        <button type="button" onClick={handleClose}>close</button>
      </form>
    </dialog>
  );
};
