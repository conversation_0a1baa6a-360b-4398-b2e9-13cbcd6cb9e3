'use client';

import React, { useState, useCallback } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { IWorksheetQuestion } from '@/apis/worksheetQuestionApi';
import { QuestionItem } from '../QuestionItem/QuestionItem';
import { handleReorderQuestionsAction } from '@/actions/worksheetQuestion.action';
import { cn } from '@/utils/cn';

export interface QuestionListProps {
  questions: IWorksheetQuestion[];
  selectedQuestions: string[];
  canEdit: boolean;
  canDelete: boolean;
  canReorder: boolean;
  canSelect: boolean;
  onEditQuestion: (question: IWorksheetQuestion) => void;
  onDeleteQuestion: (question: IWorksheetQuestion) => void;
  onSelectQuestion: (questionId: string, selected: boolean) => void;
  onReorderQuestions: (reorderedQuestions: IWorksheetQuestion[]) => void;
  worksheetId: string;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  className?: string;
}

export const QuestionList: React.FC<QuestionListProps> = ({
  questions,
  selectedQuestions,
  canEdit,
  canDelete,
  canReorder,
  canSelect,
  onEditQuestion,
  onDeleteQuestion,
  onSelectQuestion,
  onReorderQuestions,
  worksheetId,
  isLoading,
  setIsLoading,
  className
}) => {
  const [draggedItem, setDraggedItem] = useState<string | null>(null);

  const handleDragStart = useCallback((start: any) => {
    setDraggedItem(start.draggableId);
  }, []);

  const handleDragEnd = useCallback(async (result: DropResult) => {
    setDraggedItem(null);

    if (!result.destination || !canReorder) {
      return;
    }

    const { source, destination } = result;

    if (source.index === destination.index) {
      return;
    }

    // Optimistically update the UI
    const reorderedQuestions = Array.from(questions);
    const [movedQuestion] = reorderedQuestions.splice(source.index, 1);
    reorderedQuestions.splice(destination.index, 0, movedQuestion);

    // Update positions
    const updatedQuestions = reorderedQuestions.map((question, index) => ({
      ...question,
      position: index + 1
    }));

    onReorderQuestions(updatedQuestions);

    // Call the server action
    try {
      setIsLoading(true);
      const response = await handleReorderQuestionsAction(worksheetId, {
        questionId: movedQuestion.id,
        newPosition: destination.index + 1
      });

      if (response.status === 'error') {
        // Revert the optimistic update on error
        onReorderQuestions(questions);
        console.error('Failed to reorder questions:', response.message);
      }
    } catch (error) {
      // Revert the optimistic update on error
      onReorderQuestions(questions);
      console.error('Error reordering questions:', error);
    } finally {
      setIsLoading(false);
    }
  }, [questions, canReorder, onReorderQuestions, worksheetId, setIsLoading]);

  if (questions.length === 0) {
    return (
      <div className={cn(
        "flex flex-col items-center justify-center py-12 px-4 text-center",
        className
      )}>
        <div className="w-16 h-16 bg-base-200 rounded-full flex items-center justify-center mb-4">
          <svg
            className="w-8 h-8 text-base-content/50"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-base-content mb-2">
          No questions yet
        </h3>
        <p className="text-base-content/70 max-w-sm">
          Start building your worksheet by adding questions. You can create different types of questions to engage your students.
        </p>
      </div>
    );
  }

  return (
    <div className={cn("question-list", className)}>
      <DragDropContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
        <Droppable droppableId="questions" isDropDisabled={!canReorder || isLoading}>
          {(provided, snapshot) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className={cn(
                "space-y-3 sm:space-y-4 p-3 sm:p-4",
                snapshot.isDraggingOver && "bg-base-200/50"
              )}
            >
              {questions.map((question, index) => (
                <Draggable
                  key={question.id}
                  draggableId={question.id}
                  index={index}
                  isDragDisabled={!canReorder || isLoading}
                >
                  {(provided, snapshot) => (
                    <QuestionItem
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      dragHandleProps={provided.dragHandleProps}
                      question={question}
                      index={index}
                      isSelected={selectedQuestions.includes(question.id)}
                      isDragging={snapshot.isDragging}
                      canEdit={canEdit}
                      canDelete={canDelete && questions.length > 1}
                      canReorder={canReorder}
                      canSelect={canSelect}
                      onEdit={() => onEditQuestion(question)}
                      onDelete={() => onDeleteQuestion(question)}
                      onSelect={(selected) => onSelectQuestion(question.id, selected)}
                      isLoading={isLoading}
                    />
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  );
};
