'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Trash2, Alert<PERSON><PERSON>gle, X, Loader2 } from 'lucide-react';
import { handleBulkDeleteQuestionsAction } from '@/actions/worksheetQuestion.action';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';

export interface BulkDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (deletedIds: string[]) => void;
  worksheetId: string;
  questionIds: string[];
  questionCount: number;
  className?: string;
}

export const BulkDeleteModal: React.FC<BulkDeleteModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  worksheetId,
  questionIds,
  questionCount,
  className
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [reason, setReason] = useState('');
  const [forceRemoval, setForceRemoval] = useState(false);
  const dialogRef = useRef<HTMLDialogElement>(null);

  // Handle dialog open/close with proper DaisyUI pattern
  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    if (isOpen) {
      dialog.showModal();
    } else {
      dialog.close();
    }
  }, [isOpen]);

  const handleBulkDelete = async () => {
    setIsDeleting(true);
    setError(null);

    try {
      const response = await handleBulkDeleteQuestionsAction(
        worksheetId,
        questionIds,
        reason || undefined,
        forceRemoval
      );

      if (response.status === 'success') {
        onSuccess(questionIds);
        handleClose();
      } else {
        setError(response.message || 'Failed to delete questions');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    if (!isDeleting) {
      setError(null);
      setReason('');
      setForceRemoval(false);
      onClose();
    }
  };

  return (
    <dialog
      ref={dialogRef}
      className={cn("modal modal-bottom sm:modal-middle", className)}
      onClose={handleClose}
    >
      <div className="modal-box w-full max-w-md p-3 sm:p-6">
        {/* Header - Mobile Optimized */}
        <div className="flex items-center justify-between mb-4 sm:mb-6">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-error/10 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-4 h-4 sm:w-5 sm:h-5 text-error" />
            </div>
            <h3 className="text-base sm:text-lg font-semibold">Delete Questions</h3>
          </div>
          <form method="dialog">
            <button
              type="button"
              onClick={handleClose}
              className="btn btn-ghost btn-sm btn-circle"
              disabled={isDeleting}
            >
              <X className="w-3 h-3 sm:w-4 sm:h-4" />
            </button>
          </form>
        </div>

        {/* Error Message */}
        {error && (
          <div className="alert alert-error mb-3 sm:mb-4">
            <span className="text-sm">{error}</span>
          </div>
        )}

        {/* Selection Summary - Mobile Optimized */}
        <div className="bg-base-200 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
          <div className="flex items-center gap-2 mb-2">
            <Trash2 className="w-4 h-4 sm:w-5 sm:h-5 text-error" />
            <span className="font-medium text-sm sm:text-base">
              {questionCount} question{questionCount !== 1 ? 's' : ''} selected
            </span>
          </div>
          <p className="text-xs sm:text-sm text-base-content/70">
            You are about to delete {questionCount} question{questionCount !== 1 ? 's' : ''} from this worksheet.
          </p>
        </div>

        {/* Warning Message - Mobile Optimized */}
        <div className="alert alert-warning mb-3 sm:mb-4">
          <AlertTriangle className="w-4 h-4 sm:w-5 sm:h-5" />
          <div>
            <h4 className="font-medium text-sm sm:text-base">Are you sure?</h4>
            <p className="text-xs sm:text-sm">
              This action cannot be undone. All selected questions will be permanently removed from the worksheet.
            </p>
          </div>
        </div>

        {/* Reason Input */}
        <div className="form-control mb-4">
          <label className="label">
            <span className="label-text">Reason for deletion (optional)</span>
          </label>
          <textarea
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            className="textarea textarea-bordered h-20"
            placeholder="Provide a reason for deleting these questions..."
            disabled={isDeleting}
            maxLength={500}
          />
          <label className="label">
            <span className="label-text-alt">{reason.length}/500 characters</span>
          </label>
        </div>

        {/* Force Removal Option */}
        <div className="form-control mb-6">
          <label className="label cursor-pointer">
            <span className="label-text">
              <div>
                <div className="font-medium">Force removal</div>
                <div className="text-sm text-base-content/70">
                  Allow deletion even if it would leave the worksheet empty
                </div>
              </div>
            </span>
            <input
              type="checkbox"
              className="checkbox checkbox-warning"
              checked={forceRemoval}
              onChange={(e) => setForceRemoval(e.target.checked)}
              disabled={isDeleting}
            />
          </label>
        </div>

        {/* Actions - Mobile Optimized */}
        <div className="flex flex-col sm:flex-row justify-end gap-2 sm:gap-3">
          <Button
            variant="ghost"
            onClick={handleClose}
            disabled={isDeleting}
            className="w-full sm:w-auto order-2 sm:order-1"
          >
            Cancel
          </Button>
          <Button
            variant="outline"
            onClick={handleBulkDelete}
            disabled={isDeleting}
            className="btn-error w-full sm:w-auto order-1 sm:order-2"
          >
            {isDeleting && <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 animate-spin mr-2" />}
            <Trash2 className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
            Delete {questionCount} Question{questionCount !== 1 ? 's' : ''}
          </Button>
        </div>
      </div>
      <form method="dialog" className="modal-backdrop">
        <button type="button" onClick={handleClose}>close</button>
      </form>
    </dialog>
  );
};
