'use client';

import React, { useState, useRef, useEffect } from 'react';
import { X, Plus, Trash2, Upload, Loader2 } from 'lucide-react';
import { useForm, useFieldArray } from 'react-hook-form';
import { IAddQuestionToWorksheetDto } from '@/apis/worksheetQuestionApi';
import { handleBulkAddQuestionsAction } from '@/actions/worksheetQuestion.action';
import { Button } from '@/components/atoms/Button/Button';
import { RichTextEditor } from '@/components/atoms/RichTextEditor/RichTextEditor';
import { cn } from '@/utils/cn';

export interface BulkAddModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (addedCount: number) => void;
  worksheetId: string;
  questionTypes: Array<{ value: string; label: string }>;
  className?: string;
}

interface BulkQuestionFormData {
  questions: Array<{
    type: string;
    content: string;
    options: Array<{ value: string }>;
    answer: string[];
    explain: string;
    points: number;
    difficulty: string;
    subject: string;
  }>;
  insertPosition?: number;
  validateQuestions: boolean;
  reason: string;
}

export const BulkAddModal: React.FC<BulkAddModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  worksheetId,
  questionTypes,
  className
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeQuestionIndex, setActiveQuestionIndex] = useState(0);
  const dialogRef = useRef<HTMLDialogElement>(null);

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors }
  } = useForm<BulkQuestionFormData>({
    defaultValues: {
      questions: [
        {
          type: '',
          content: '',
          options: [{ value: '' }, { value: '' }],
          answer: [],
          explain: '',
          points: 1,
          difficulty: 'MEDIUM',
          subject: ''
        }
      ],
      validateQuestions: true,
      reason: 'Bulk add questions'
    }
  });

  const { fields: questionFields, append: appendQuestion, remove: removeQuestion } = useFieldArray({
    control,
    name: 'questions'
  });

  // Handle dialog open/close
  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    if (isOpen) {
      dialog.showModal();
    } else {
      dialog.close();
    }
  }, [isOpen]);

  const onSubmit = async (data: BulkQuestionFormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const questionsData: IAddQuestionToWorksheetDto[] = data.questions.map(q => ({
        type: q.type as any,
        content: q.content,
        options: q.options.map(opt => opt.value).filter(Boolean),
        answer: q.answer,
        explain: q.explain,
        points: q.points,
        difficulty: q.difficulty as any,
        subject: q.subject
      }));

      const response = await handleBulkAddQuestionsAction(
        worksheetId,
        questionsData,
        data.insertPosition,
        data.validateQuestions,
        data.reason
      );

      if (response.status === 'success') {
        onSuccess(response.data?.successCount || questionsData.length);
        handleClose();
      } else {
        setError(response.message || 'Failed to add questions');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const addQuestion = () => {
    appendQuestion({
      type: '',
      content: '',
      options: [{ value: '' }, { value: '' }],
      answer: [],
      explain: '',
      points: 1,
      difficulty: 'MEDIUM',
      subject: ''
    });
    setActiveQuestionIndex(questionFields.length);
  };

  const removeQuestionAt = (index: number) => {
    if (questionFields.length > 1) {
      removeQuestion(index);
      if (activeQuestionIndex >= questionFields.length - 1) {
        setActiveQuestionIndex(Math.max(0, questionFields.length - 2));
      }
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setError(null);
      setActiveQuestionIndex(0);
      reset();
      onClose();
    }
  };

  return (
    <dialog
      ref={dialogRef}
      className={cn("modal modal-bottom sm:modal-middle", className)}
      onClose={handleClose}
    >
      <div className="modal-box w-full !max-w-[1200px] max-h-[95vh] overflow-y-auto p-3 sm:p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-3 sm:mb-4">
          <h3 className="text-base sm:text-lg font-semibold">
            Bulk Add Questions ({questionFields.length})
          </h3>
          <form method="dialog">
            <button
              type="button"
              onClick={handleClose}
              className="btn btn-ghost btn-circle btn-sm sm:btn-md"
              disabled={isSubmitting}
            >
              <X className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500" />
            </button>
          </form>
        </div>

        {/* Error Message */}
        {error && (
          <div className="alert alert-error mb-3">
            <span>{error}</span>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Question Tabs */}
          <div className="flex flex-wrap gap-1 border-b border-base-300 pb-2">
            {questionFields.map((_, index) => (
              <button
                key={index}
                type="button"
                onClick={() => setActiveQuestionIndex(index)}
                className={cn(
                  "btn btn-xs",
                  activeQuestionIndex === index ? "btn-primary" : "btn-ghost"
                )}
              >
                Q{index + 1}
              </button>
            ))}
            <button
              type="button"
              onClick={addQuestion}
              className="btn btn-xs btn-ghost text-primary"
              disabled={isSubmitting}
            >
              <Plus className="w-3 h-3" />
            </button>
          </div>

          {/* Active Question Form */}
          {questionFields.map((field, index) => (
            <div
              key={field.id}
              className={cn(
                "space-y-4",
                activeQuestionIndex !== index && "hidden"
              )}
            >
              {/* Question Header */}
              <div className="flex items-center justify-between">
                <h4 className="text-lg font-medium">Question {index + 1}</h4>
                {questionFields.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeQuestionAt(index)}
                    className="btn btn-ghost btn-sm text-error"
                    disabled={isSubmitting}
                  >
                    <Trash2 className="w-4 h-4" />
                    Remove
                  </button>
                )}
              </div>

              {/* Question Type and Points */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">Question Type *</span>
                  </label>
                  <select
                    {...register(`questions.${index}.type`, { required: 'Question type is required' })}
                    className="select select-bordered select-sm"
                    disabled={isSubmitting}
                  >
                    <option value="">Select question type</option>
                    {questionTypes.map(type => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">Points</span>
                  </label>
                  <input
                    type="number"
                    {...register(`questions.${index}.points`, { min: 0, max: 100 })}
                    className="input input-bordered input-sm"
                    min="0"
                    max="100"
                    disabled={isSubmitting}
                  />
                </div>
              </div>

              {/* Question Content */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium">Question Content *</span>
                </label>
                <textarea
                  {...register(`questions.${index}.content`, { required: 'Question content is required' })}
                  className="textarea textarea-bordered"
                  rows={3}
                  placeholder="Enter your question here..."
                  disabled={isSubmitting}
                />
              </div>

              {/* Answer and Explanation */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">Answer *</span>
                  </label>
                  <textarea
                    {...register(`questions.${index}.answer.0`, { required: 'Answer is required' })}
                    className="textarea textarea-bordered"
                    rows={2}
                    placeholder="Enter the correct answer..."
                    disabled={isSubmitting}
                  />
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">Explanation *</span>
                  </label>
                  <textarea
                    {...register(`questions.${index}.explain`, { required: 'Explanation is required' })}
                    className="textarea textarea-bordered"
                    rows={2}
                    placeholder="Explain why this is correct..."
                    disabled={isSubmitting}
                  />
                </div>
              </div>
            </div>
          ))}

          {/* Bulk Options */}
          <div className="bg-base-50 p-4 rounded-lg space-y-3">
            <h4 className="font-medium">Bulk Options</h4>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Insert Position</span>
                </label>
                <input
                  type="number"
                  {...register('insertPosition')}
                  className="input input-bordered input-sm"
                  placeholder="End of list"
                  disabled={isSubmitting}
                />
              </div>

              <div className="form-control">
                <label className="label cursor-pointer">
                  <span className="label-text">Validate Questions</span>
                  <input
                    type="checkbox"
                    {...register('validateQuestions')}
                    className="checkbox checkbox-primary"
                    disabled={isSubmitting}
                  />
                </label>
              </div>
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Reason</span>
              </label>
              <input
                {...register('reason')}
                className="input input-bordered input-sm"
                placeholder="Reason for bulk add..."
                disabled={isSubmitting}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row justify-end gap-2 sm:gap-3 pt-4 border-t border-base-200">
            <Button
              type="button"
              variant="ghost"
              onClick={handleClose}
              disabled={isSubmitting}
              className="btn-sm w-full sm:w-auto"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={isSubmitting}
              className="btn-primary btn-sm w-full sm:w-auto"
            >
              {isSubmitting && <Loader2 className="w-4 h-4 animate-spin mr-2" />}
              Add {questionFields.length} Question{questionFields.length !== 1 ? 's' : ''}
            </Button>
          </div>
        </form>
      </div>
      <form method="dialog" className="modal-backdrop">
        <button type="button" onClick={handleClose}>close</button>
      </form>
    </dialog>
  );
};
