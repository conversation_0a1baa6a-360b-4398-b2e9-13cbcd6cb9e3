'use client';

import React, { forwardRef } from 'react';
import { Edit, Trash2, GripVertical, Copy } from 'lucide-react';
import { IWorksheetQuestion } from '@/apis/worksheetQuestionApi';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';

export interface QuestionItemProps {
  question: IWorksheetQuestion;
  index: number;
  isSelected: boolean;
  isDragging: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canReorder: boolean;
  canSelect: boolean;
  onEdit: () => void;
  onDelete: () => void;
  onSelect: (selected: boolean) => void;
  onDuplicate?: () => void;
  dragHandleProps?: any;
  isLoading: boolean;
  className?: string;
}

const QuestionItemComponent = ({
  question,
  index,
  isSelected,
  isDragging,
  canEdit,
  canDelete,
  canReorder,
  canSelect,
  onEdit,
  onDelete,
  onSelect,
  onDuplicate,
  dragHandleProps,
  isLoading,
  className,
  ...props
}: QuestionItemProps, ref: React.Ref<HTMLDivElement>) => {
  const getQuestionTypeLabel = (type: string) => {
    const typeMap: Record<string, string> = {
      'MULTIPLE_CHOICE': 'Multiple Choice',
      'TRUE_FALSE': 'True/False',
      'FILL_IN_BLANK': 'Fill in the Blank',
      'SHORT_ANSWER': 'Short Answer',
      'ESSAY': 'Essay',
      'MATCHING': 'Matching',
      'ORDERING': 'Ordering'
    };
    return typeMap[type] || type;
  };

  const getQuestionTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      'MULTIPLE_CHOICE': 'badge-primary',
      'TRUE_FALSE': 'badge-secondary',
      'FILL_IN_BLANK': 'badge-accent',
      'SHORT_ANSWER': 'badge-info',
      'ESSAY': 'badge-warning',
      'MATCHING': 'badge-success',
      'ORDERING': 'badge-error'
    };
    return colorMap[type] || 'badge-neutral';
  };

  const getDifficultyColor = (difficulty?: string) => {
    const colorMap: Record<string, string> = {
      'EASY': 'text-success',
      'MEDIUM': 'text-warning',
      'HARD': 'text-error'
    };
    return difficulty ? colorMap[difficulty] || 'text-base-content' : 'text-base-content';
  };

  return (
    <div
      ref={ref}
      {...props}
      className={cn(
        "card bg-base-100 border border-base-300 shadow-sm transition-all duration-200",
        isDragging && "shadow-lg rotate-2 scale-105",
        isSelected && "ring-2 ring-primary ring-opacity-50",
        "hover:shadow-md",
        className
      )}
    >
      <div className="card-body p-3 sm:p-4">
        {/* Question Header - Mobile Optimized */}
        <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-2 sm:gap-4">
          <div className="flex items-center gap-2 sm:gap-3 flex-1 min-w-0">
            {/* Drag Handle */}
            {canReorder && (
              <div
                {...dragHandleProps}
                className="cursor-grab active:cursor-grabbing text-base-content/50 hover:text-base-content transition-colors"
                aria-label="Drag to reorder"
              >
                <GripVertical className="w-4 h-4 sm:w-5 sm:h-5" />
              </div>
            )}

            {/* Selection Checkbox */}
            {canSelect && (
              <input
                type="checkbox"
                className="checkbox checkbox-primary checkbox-sm sm:checkbox-md"
                checked={isSelected}
                onChange={(e) => onSelect(e.target.checked)}
                disabled={isLoading}
                aria-label={`Select question ${index + 1}`}
              />
            )}

            {/* Question Number and Type */}
            <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
              <span className="font-medium text-base-content text-sm sm:text-base">
                Q{index + 1}
              </span>
              <div className={cn("badge badge-xs sm:badge-sm", getQuestionTypeColor(question.type))}>
                <span className="hidden sm:inline">{getQuestionTypeLabel(question.type)}</span>
                <span className="sm:hidden">{getQuestionTypeLabel(question.type).substring(0, 3)}</span>
              </div>
              {question.difficulty && (
                <span className={cn("text-xs font-medium", getDifficultyColor(question.difficulty))}>
                  <span className="hidden sm:inline">{question.difficulty}</span>
                  <span className="sm:hidden">{question.difficulty.charAt(0)}</span>
                </span>
              )}
            </div>
          </div>

          {/* Action Buttons - Mobile Optimized */}
          <div className="flex items-center gap-1 self-end sm:self-start">
            {onDuplicate && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onDuplicate}
                disabled={isLoading}
                className="btn-ghost btn-xs sm:btn-sm p-1 sm:p-2"
                aria-label="Duplicate question"
              >
                <Copy className="w-3 h-3 sm:w-4 sm:h-4" />
              </Button>
            )}

            {canEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log('Edit button clicked for question:', question.id);
                  onEdit();
                }}
                disabled={isLoading}
                className="btn-ghost btn-xs sm:btn-sm p-1 sm:p-2 hover:bg-blue-50 hover:text-blue-600"
                aria-label="Edit question"
              >
                <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
              </Button>
            )}

            {canDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log('Delete button clicked for question:', question.id);
                  onDelete();
                }}
                disabled={isLoading}
                className="btn-ghost btn-xs sm:btn-sm p-1 sm:p-2 text-error hover:bg-error/10 hover:text-error"
                aria-label="Delete question"
              >
                <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
              </Button>
            )}
          </div>
        </div>

        {/* Question Content - Mobile Optimized */}
        <div className="mt-2 sm:mt-3">
          <div className="prose prose-sm max-w-none">
            <p className="text-base-content mb-2 line-clamp-2 sm:line-clamp-3 text-sm sm:text-base">
              {question.content}
            </p>
          </div>

          {/* Question Options Preview - Mobile Optimized */}
          {question.options && question.options.length > 0 && (
            <div className="mt-2">
              <div className="text-xs text-base-content/70 mb-1">Options:</div>
              <div className="flex flex-wrap gap-1">
                {question.options.slice(0, 2).map((option, idx) => (
                  <span
                    key={idx}
                    className={cn(
                      "badge badge-outline badge-xs sm:badge-sm",
                      question.answer?.includes(option) && "badge-success"
                    )}
                  >
                    {option.length > 15 ? `${option.substring(0, 15)}...` : option}
                  </span>
                ))}
                {question.options.length > 2 && (
                  <span className="badge badge-ghost badge-xs sm:badge-sm">
                    +{question.options.length - 2} more
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Question Metadata - Mobile Optimized */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-2 sm:mt-3 text-xs text-base-content/70 gap-1 sm:gap-4">
            <div className="flex items-center gap-2 sm:gap-4 flex-wrap">
              {question.points && (
                <span>{question.points} pt{question.points !== 1 ? 's' : ''}</span>
              )}
              {question.subject && (
                <span className="truncate max-w-[100px] sm:max-w-none">{question.subject}</span>
              )}
              {question.grade && (
                <span className="hidden sm:inline">{question.grade}</span>
              )}
            </div>
            {question.position && (
              <span className="text-xs">Pos: {question.position}</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export const QuestionItem = forwardRef<HTMLDivElement, QuestionItemProps>(QuestionItemComponent);
QuestionItem.displayName = 'QuestionItem';
