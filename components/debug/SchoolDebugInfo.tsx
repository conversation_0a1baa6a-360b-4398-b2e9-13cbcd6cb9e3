'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { getMySchool } from '@/actions/school.action';

export const SchoolDebugInfo: React.FC = () => {
  const { data: session } = useSession();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleTestSchoolFetch = async () => {
    setIsLoading(true);
    try {
      const result = await getMySchool();
      setDebugInfo({
        timestamp: new Date().toISOString(),
        session: {
          userId: session?.user?.id,
          userRole: session?.user?.role,
          userSchoolId: session?.user?.schoolId,
          hasAccessToken: !!session?.user?.accessToken,
        },
        apiResult: result,
      });
    } catch (error) {
      setDebugInfo({
        timestamp: new Date().toISOString(),
        session: {
          userId: session?.user?.id,
          userRole: session?.user?.role,
          userSchoolId: session?.user?.schoolId,
          hasAccessToken: !!session?.user?.accessToken,
        },
        error: error instanceof Error ? error.message : String(error),
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="card bg-base-100 shadow-md">
      <div className="card-body">
        <h3 className="card-title text-sm">School Debug Info</h3>
        
        <div className="space-y-2 text-xs">
          <div>
            <strong>Session Info:</strong>
            <pre className="bg-base-200 p-2 rounded text-xs overflow-auto">
              {JSON.stringify({
                userId: session?.user?.id,
                userRole: session?.user?.role,
                userSchoolId: session?.user?.schoolId,
                hasAccessToken: !!session?.user?.accessToken,
              }, null, 2)}
            </pre>
          </div>
          
          <button 
            className={`btn btn-sm btn-primary ${isLoading ? 'loading' : ''}`}
            onClick={handleTestSchoolFetch}
            disabled={isLoading}
          >
            Test School Fetch
          </button>
          
          {debugInfo && (
            <div>
              <strong>API Test Result:</strong>
              <pre className="bg-base-200 p-2 rounded text-xs overflow-auto max-h-40">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SchoolDebugInfo;
