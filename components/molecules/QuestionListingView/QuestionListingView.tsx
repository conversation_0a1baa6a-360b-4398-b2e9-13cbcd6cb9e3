'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { 
  Edit, 
  Trash2, 
  GripVertical, 
  ChevronUp, 
  ChevronDown 
} from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';
import { IWorksheetQuestion } from '@/apis/worksheetQuestionApi';
import { handleDeleteQuestionAction, handleReorderQuestionsAction } from '@/actions/worksheetQuestion.action';
import { DeleteQuestionModal } from './DeleteQuestionModal';
import { QuestionFormModal } from '../../organisms/WorksheetQuestionManager/QuestionFormModal/QuestionFormModal';

// Question type definition
export type Question = {
  id: string; // Required ID field for API integration
  type: 'multiple_choice' | 'single_choice' | 'fill_blank' | 'creative_writing' | 'true_false';
  content: string;
  options?: string[];
  answer?: string[];
  explain?: string;
  image?: string | null;
  imagePrompt?: string | null;
  subject: string;
  prompt?: string;
};

// Worksheet info type
export type WorksheetInfo = {
  subject?: string;
  topic?: string;
  grade?: string;
  level?: string;
  language?: string;
  totalQuestions?: number;
};

// Component props
export interface QuestionListingViewProps {
  questions: Question[];
  worksheetInfo?: WorksheetInfo;
  worksheetId?: string;
  isReadOnly?: boolean;
  isHtmlContent?: boolean;
  containerClass?: string;
  onQuestionsChange?: (questions: Question[]) => void;
}

// Helper function to ensure questions have IDs
const ensureQuestionIds = (questions: Question[]): Question[] => {
  return questions.map((question, index) => {
    // If question already has an ID, keep it
    if (question.id && question.id.trim() !== '') {
      console.log(`✅ Question ${index + 1} has real ID:`, question.id.slice(0, 8) + '...');
      return question;
    }

    // Only assign temporary ID if no real ID exists
    const tempId = `temp-question-${Date.now()}-${index}`;
    console.log(`⚠️ Question ${index + 1} missing ID, assigning temporary:`, tempId);
    return {
      ...question,
      id: tempId
    };
  });
};

const QuestionListingView: React.FC<QuestionListingViewProps> = ({
  questions,
  worksheetInfo,
  worksheetId,
  isReadOnly = false,
  isHtmlContent = false,
  containerClass,
  onQuestionsChange,
}) => {
  // Ensure all questions have IDs
  const questionsWithIds = ensureQuestionIds(questions);

  // State management
  const [localQuestions, setLocalQuestions] = useState<Question[]>(questionsWithIds);
  const [isLoading, setIsLoading] = useState(false);
  const [deletingQuestionId, setDeletingQuestionId] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [questionToDelete, setQuestionToDelete] = useState<Question | null>(null);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);

  // Update local questions when props change
  useEffect(() => {
    const questionsWithIds = ensureQuestionIds(questions);
    setLocalQuestions(questionsWithIds);
  }, [questions]);

  // Handle drag and drop
  const handleDragEnd = useCallback(async (result: DropResult) => {
    console.log('🔄 Drag end called with:', {
      result,
      worksheetId,
      isReadOnly,
      hasDestination: !!result.destination,
      sourceIndex: result.source?.index,
      destinationIndex: result.destination?.index
    });

    if (!result.destination) {
      console.log('❌ No destination, aborting drag');
      return;
    }

    if (!worksheetId) {
      console.warn('❌ No worksheetId provided for drag operation');
      return;
    }

    if (isReadOnly) {
      console.warn('❌ Component is in read-only mode');
      return;
    }

    const { source, destination } = result;
    if (source.index === destination.index) {
      console.log('❌ Same position, no change needed');
      return;
    }

    const newQuestions = Array.from(localQuestions);
    const [reorderedItem] = newQuestions.splice(source.index, 1);
    newQuestions.splice(destination.index, 0, reorderedItem);

    console.log('📝 Reordered item:', {
      item: reorderedItem,
      hasId: !!reorderedItem.id,
      fromIndex: source.index,
      toIndex: destination.index,
      newPosition: destination.index + 1
    });

    // Optimistically update UI
    setLocalQuestions(newQuestions);
    onQuestionsChange?.(newQuestions);

    // Call API to persist reordering
    try {
      setIsLoading(true);

      // Check if we should persist to API
      const shouldPersist = reorderedItem.id && !reorderedItem.id.startsWith('temp-question-') && worksheetId;

      console.log('🔍 API persistence check:', {
        hasId: !!reorderedItem.id,
        isTemporary: reorderedItem.id?.startsWith('temp-question-'),
        hasWorksheetId: !!worksheetId,
        shouldPersist,
        questionId: reorderedItem.id
      });

      if (shouldPersist) {
        console.log('🚀 Calling API to reorder question:', {
          worksheetId,
          questionId: reorderedItem.id,
          newPosition: destination.index + 1
        });

        const response = await handleReorderQuestionsAction(
          worksheetId,
          {
            reorders: [{
              questionId: reorderedItem.id,
              newPosition: destination.index + 1
            }]
          }
        );

        console.log('📡 API response:', response);

        if (response.status !== 'success') {
          // Revert on failure
          setLocalQuestions(localQuestions);
          onQuestionsChange?.(localQuestions);
          console.error('❌ Failed to reorder question:', response.message);
          alert(`Failed to reorder question: ${response.message}`);
        } else {
          console.log('✅ Question reordered successfully and persisted to API');
        }
      } else {
        // Log why we're not persisting
        if (!reorderedItem.id) {
          console.warn('❌ Question has no ID, cannot persist reorder');
          alert('Cannot save reorder: Question has no ID');
        } else if (reorderedItem.id.startsWith('temp-question-')) {
          console.warn('⚠️ Question has temporary ID, reorder will not be persisted to server');
          console.log('✅ Question reordered locally only (temporary ID)');
        } else if (!worksheetId) {
          console.warn('⚠️ No worksheetId provided, reorder will not be persisted to server');
          console.log('✅ Question reordered locally only (no worksheetId)');
        }
      }
    } catch (error) {
      // Revert on error
      setLocalQuestions(localQuestions);
      onQuestionsChange?.(localQuestions);
      console.error('❌ Failed to reorder question:', error);
      alert('Failed to reorder question. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [localQuestions, worksheetId, onQuestionsChange, isReadOnly]);

  // Handle move up/down
  const handleMoveUp = useCallback(async (index: number) => {
    console.log('⬆️ Move up called with:', {
      index,
      worksheetId,
      isReadOnly,
      isFirstItem: index === 0,
      totalQuestions: localQuestions.length
    });

    if (index === 0) {
      console.log('❌ Cannot move up: Already at top');
      return;
    }

    if (!worksheetId) {
      console.warn('❌ No worksheetId provided for move up operation');
      return;
    }

    if (isReadOnly) {
      console.warn('❌ Component is in read-only mode');
      return;
    }

    const questionToMove = localQuestions[index];
    console.log('📝 Question to move up:', {
      question: questionToMove,
      hasId: !!questionToMove.id,
      currentIndex: index,
      newIndex: index - 1
    });

    if (!questionToMove.id) {
      console.warn('❌ Question has no ID, cannot persist move up');
      alert('Cannot save reorder: Question has no ID');
      return;
    }

    if (questionToMove.id.startsWith('temp-question-')) {
      console.warn('⚠️ Question has temporary ID, move up will not be persisted to server');
    }

    const newQuestions = [...localQuestions];
    [newQuestions[index - 1], newQuestions[index]] = [newQuestions[index], newQuestions[index - 1]];

    // Optimistically update UI
    setLocalQuestions(newQuestions);
    onQuestionsChange?.(newQuestions);

    // Check if we should persist to API
    const shouldPersist = questionToMove.id && !questionToMove.id.startsWith('temp-question-') && worksheetId;

    console.log('🔍 Move up API persistence check:', {
      hasId: !!questionToMove.id,
      isTemporary: questionToMove.id?.startsWith('temp-question-'),
      hasWorksheetId: !!worksheetId,
      shouldPersist,
      questionId: questionToMove.id
    });

    if (shouldPersist) {
      try {
        setIsLoading(true);

        console.log('🚀 Calling API to move question up:', {
          worksheetId,
          questionId: questionToMove.id,
          newPosition: index // Moving up means new position is current index - 1 + 1 = index
        });

        const response = await handleReorderQuestionsAction(
          worksheetId,
          {
            reorders: [{
              questionId: questionToMove.id,
              newPosition: index // Moving up means new position is current index - 1 + 1 = index
            }]
          }
        );

        console.log('📡 Move up API response:', response);

        if (response.status !== 'success') {
          // Revert on failure
          setLocalQuestions(localQuestions);
          onQuestionsChange?.(localQuestions);
          console.error('❌ Failed to move question up:', response.message);
          alert(`Failed to move question up: ${response.message}`);
        } else {
          console.log('✅ Question moved up successfully and persisted to API');
        }
      } catch (error) {
        // Revert on error
        setLocalQuestions(localQuestions);
        onQuestionsChange?.(localQuestions);
        console.error('❌ Failed to move question up:', error);
        alert('Failed to move question up. Please try again.');
      } finally {
        setIsLoading(false);
      }
    } else {
      // Log why we're not persisting
      if (!questionToMove.id) {
        console.warn('❌ Question has no ID, cannot persist move up');
      } else if (questionToMove.id.startsWith('temp-question-')) {
        console.log('✅ Question moved up locally only (temporary ID)');
      } else if (!worksheetId) {
        console.warn('⚠️ No worksheetId provided, move up will not be persisted');
        console.log('✅ Question moved up locally only (no worksheetId)');
      }
    }
  }, [localQuestions, worksheetId, onQuestionsChange, isReadOnly]);

  const handleMoveDown = useCallback(async (index: number) => {
    console.log('⬇️ Move down called with:', {
      index,
      worksheetId,
      isReadOnly,
      isLastItem: index === localQuestions.length - 1,
      totalQuestions: localQuestions.length
    });

    if (index === localQuestions.length - 1) {
      console.log('❌ Cannot move down: Already at bottom');
      return;
    }

    if (!worksheetId) {
      console.warn('❌ No worksheetId provided for move down operation');
      return;
    }

    if (isReadOnly) {
      console.warn('❌ Component is in read-only mode');
      return;
    }

    const questionToMove = localQuestions[index];
    console.log('📝 Question to move down:', {
      question: questionToMove,
      hasId: !!questionToMove.id,
      currentIndex: index,
      newIndex: index + 1
    });

    if (!questionToMove.id) {
      console.warn('❌ Question has no ID, cannot persist move down');
      alert('Cannot save reorder: Question has no ID');
      return;
    }

    if (questionToMove.id.startsWith('temp-question-')) {
      console.warn('⚠️ Question has temporary ID, move down will not be persisted to server');
    }

    const newQuestions = [...localQuestions];
    [newQuestions[index], newQuestions[index + 1]] = [newQuestions[index + 1], newQuestions[index]];

    // Optimistically update UI
    setLocalQuestions(newQuestions);
    onQuestionsChange?.(newQuestions);

    // Check if we should persist to API
    const shouldPersist = questionToMove.id && !questionToMove.id.startsWith('temp-question-') && worksheetId;

    console.log('🔍 Move down API persistence check:', {
      hasId: !!questionToMove.id,
      isTemporary: questionToMove.id?.startsWith('temp-question-'),
      hasWorksheetId: !!worksheetId,
      shouldPersist,
      questionId: questionToMove.id
    });

    if (shouldPersist) {
      try {
        setIsLoading(true);

        console.log('🚀 Calling API to move question down:', {
          worksheetId,
          questionId: questionToMove.id,
          newPosition: index + 2 // Moving down means new position is current index + 1 + 1 = index + 2
        });

        const response = await handleReorderQuestionsAction(
          worksheetId,
          {
            reorders: [{
              questionId: questionToMove.id,
              newPosition: index + 2 // Moving down means new position is current index + 1 + 1 = index + 2
            }]
          }
        );

        console.log('📡 Move down API response:', response);

        if (response.status !== 'success') {
          // Revert on failure
          setLocalQuestions(localQuestions);
          onQuestionsChange?.(localQuestions);
          console.error('❌ Failed to move question down:', response.message);
          alert(`Failed to move question down: ${response.message}`);
        } else {
          console.log('✅ Question moved down successfully and persisted to API');
        }
      } catch (error) {
        // Revert on error
        setLocalQuestions(localQuestions);
        onQuestionsChange?.(localQuestions);
        console.error('❌ Failed to move question down:', error);
        alert('Failed to move question down. Please try again.');
      } finally {
        setIsLoading(false);
      }
    } else {
      // Log why we're not persisting
      if (!questionToMove.id) {
        console.warn('❌ Question has no ID, cannot persist move down');
      } else if (questionToMove.id.startsWith('temp-question-')) {
        console.log('✅ Question moved down locally only (temporary ID)');
      } else if (!worksheetId) {
        console.warn('⚠️ No worksheetId provided, move down will not be persisted');
        console.log('✅ Question moved down locally only (no worksheetId)');
      }
    }
  }, [localQuestions, worksheetId, onQuestionsChange, isReadOnly]);

  // Handle question deletion
  const handleDeleteQuestion = useCallback((question: Question) => {
    console.log('Delete handler called with:', {
      question,
      worksheetId,
      isReadOnly,
      questionId: question.id
    });

    // Allow modal to show for debugging, but warn about missing data
    if (!worksheetId) {
      console.warn('No worksheetId provided for delete operation');
    }
    if (isReadOnly) {
      console.warn('Component is in read-only mode');
      return;
    }
    if (!question.id) {
      console.warn('Question has no ID, delete operation may fail');
    }

    setQuestionToDelete(question);
    setShowDeleteModal(true);
  }, [worksheetId, isReadOnly]);

  const confirmDeleteQuestion = useCallback(async () => {
    console.log('Confirm delete called with:', { questionToDelete, worksheetId });

    if (!questionToDelete) {
      console.error('No question to delete');
      setShowDeleteModal(false);
      return;
    }

    if (!worksheetId) {
      console.error('No worksheetId provided for delete operation');
      alert('Cannot delete question: Missing worksheet ID');
      setShowDeleteModal(false);
      return;
    }

    if (!questionToDelete.id) {
      console.error('Question has no ID, cannot delete');
      alert('Cannot delete question: Question has no ID');
      setShowDeleteModal(false);
      return;
    }

    try {
      setDeletingQuestionId(questionToDelete.id);
      setIsLoading(true);

      // Call API to delete question using proper question ID
      const response = await handleDeleteQuestionAction(
        worksheetId,
        questionToDelete.id,
        'Deleted from question listing view'
      );

      if (response.status === 'success') {
        const newQuestions = localQuestions.filter(q => q.id !== questionToDelete.id);
        setLocalQuestions(newQuestions);
        onQuestionsChange?.(newQuestions);
      } else {
        console.error('Delete failed:', response.message);
        alert(`Failed to delete question: ${response.message}`);
      }
    } catch (error) {
      console.error('Failed to delete question:', error);
      alert('Failed to delete question. Please try again.');
    } finally {
      setDeletingQuestionId(null);
      setIsLoading(false);
      setShowDeleteModal(false);
      setQuestionToDelete(null);
    }
  }, [questionToDelete, worksheetId, localQuestions, onQuestionsChange]);

  const cancelDeleteQuestion = useCallback(() => {
    setShowDeleteModal(false);
    setQuestionToDelete(null);
  }, []);

  // Handle question editing
  const handleEditQuestion = useCallback((question: Question) => {
    console.log('Edit handler called with:', {
      question,
      worksheetId,
      isReadOnly,
      questionId: question.id
    });

    // Allow modal to show for debugging, but warn about missing data
    if (!worksheetId) {
      console.warn('No worksheetId provided for edit operation');
    }
    if (isReadOnly) {
      console.warn('Component is in read-only mode');
      return;
    }
    if (!question.id) {
      console.warn('Question has no ID, edit operation may fail');
    }

    console.log('Opening edit modal for question:', question);
    setEditingQuestion(question);
    setShowEditModal(true);
  }, [worksheetId, isReadOnly]);

  const handleEditSuccess = useCallback((updatedQuestion: IWorksheetQuestion) => {
    if (!editingQuestion || !editingQuestion.id) return;

    // Convert the updated question back to the Question format
    const convertedQuestion: Question = {
      id: editingQuestion.id,
      type: updatedQuestion.type.toLowerCase().replace(/([A-Z])/g, '_$1').replace(/^_/, '') as any,
      content: updatedQuestion.content,
      options: updatedQuestion.options || [],
      answer: updatedQuestion.answer || [],
      explain: updatedQuestion.explain || '',
      image: updatedQuestion.imageUrl || null,
      imagePrompt: updatedQuestion.imagePrompt || null,
      subject: updatedQuestion.subject || '',
    };

    const updatedQuestions = localQuestions.map(q =>
      q.id === editingQuestion.id ? convertedQuestion : q
    );

    setLocalQuestions(updatedQuestions);
    onQuestionsChange?.(updatedQuestions);
    setShowEditModal(false);
    setEditingQuestion(null);
  }, [editingQuestion, localQuestions, onQuestionsChange]);

  const cancelEditQuestion = useCallback(() => {
    setShowEditModal(false);
    setEditingQuestion(null);
  }, []);

  return (
    <>
      {/* Debug Info - Remove this in production */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed top-4 right-4 bg-yellow-100 border border-yellow-400 rounded p-2 text-xs z-50 max-w-xs overflow-y-auto max-h-96">
          <div><strong>🐛 Debug Info:</strong></div>
          <div>worksheetId: {worksheetId || 'undefined'}</div>
          <div>isReadOnly: {isReadOnly.toString()}</div>
          <div>isLoading: {isLoading.toString()}</div>
          <div>showDeleteModal: {showDeleteModal.toString()}</div>
          <div>showEditModal: {showEditModal.toString()}</div>
          <div>editingQuestion: {editingQuestion ? 'set' : 'null'}</div>
          <div>questionToDelete: {questionToDelete ? 'set' : 'null'}</div>
          <div>questions count: {localQuestions.length}</div>

          <div className="mt-2"><strong>📝 Questions & IDs:</strong></div>
          {localQuestions.slice(0, 3).map((q, i) => {
            const isTemp = q.id?.startsWith('temp-question-');
            const canPersist = q.id && !isTemp && worksheetId;
            return (
              <div key={i} className="ml-2 border-l pl-2 mb-1">
                <div>Q{i + 1}: {q.id ? `${q.id.slice(0, 12)}...` : 'NO ID'}</div>
                <div className={isTemp ? 'text-orange-600' : 'text-green-600'}>
                  {isTemp ? '⚠️ TEMP' : '✅ REAL'} ID
                </div>
                <div className={canPersist ? 'text-green-600' : 'text-red-600'}>
                  {canPersist ? '✅ CAN PERSIST' : '❌ NO PERSIST'}
                </div>
                <div>Type: {q.type}</div>
                <div>Content: {q.content.slice(0, 25)}...</div>
              </div>
            );
          })}
          {localQuestions.length > 3 && <div className="ml-2">... and {localQuestions.length - 3} more</div>}

          <div className="mt-2 pt-2 border-t">
            <div><strong>🔧 API Status:</strong></div>
            <div>Can make API calls: {(worksheetId && !isReadOnly) ? '✅ YES' : '❌ NO'}</div>
            <div>Real questions: {localQuestions.filter(q => q.id && !q.id.startsWith('temp-question-')).length}</div>
            <div>Temp questions: {localQuestions.filter(q => q.id?.startsWith('temp-question-')).length}</div>
          </div>
        </div>
      )}

      <div className={cn('space-y-8', containerClass)}>
        {/* Worksheet Information - Sticky and Mobile-Optimized */}
        {worksheetInfo && (
          <div className="sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm mb-4">
            {/* Mobile Layout - Minimal and Compact */}
            <div className="block md:hidden px-3 py-2">
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center gap-2">
                  {worksheetInfo.subject && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
                      {worksheetInfo.subject}
                    </span>
                  )}
                  {worksheetInfo.grade && (
                    <span className="px-2 py-1 bg-green-100 text-green-700 rounded-full font-medium">
                      {worksheetInfo.grade}
                    </span>
                  )}
                </div>
                {worksheetInfo.totalQuestions && (
                  <span className="text-gray-600 font-medium">
                    {worksheetInfo.totalQuestions} Q
                  </span>
                )}
              </div>
            </div>

            {/* Desktop Layout - Full Details */}
            <div className="hidden md:block p-3">
              <div className="flex flex-wrap gap-x-6 gap-y-1 text-sm">
                {worksheetInfo.subject && (
                  <div className="flex items-center">
                    <span className="font-medium text-gray-600">Subject:</span>
                    <span className="ml-2 text-primary">{worksheetInfo.subject}</span>
                  </div>
                )}
                {worksheetInfo.topic && (
                  <div className="flex items-center">
                    <span className="font-medium text-gray-600">Topic:</span>
                    <span className="ml-2 text-primary">{worksheetInfo.topic}</span>
                  </div>
                )}
                {worksheetInfo.grade && (
                  <div className="flex items-center">
                    <span className="font-medium text-gray-600">Grade:</span>
                    <span className="ml-2 text-primary">{worksheetInfo.grade}</span>
                  </div>
                )}
                {worksheetInfo.level && (
                  <div className="flex items-center">
                    <span className="font-medium text-gray-600">Level:</span>
                    <span className="ml-2 text-primary">{worksheetInfo.level}</span>
                  </div>
                )}
                {worksheetInfo.language && (
                  <div className="flex items-center">
                    <span className="font-medium text-gray-600">Language:</span>
                    <span className="ml-2 text-primary">{worksheetInfo.language}</span>
                  </div>
                )}
                {worksheetInfo.totalQuestions && (
                  <div className="flex items-center">
                    <span className="font-medium text-gray-600">Total Questions:</span>
                    <span className="ml-2 text-primary">{worksheetInfo.totalQuestions}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
        
        {/* Questions List with drag-and-drop enabled by default */}
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="questions" isDropDisabled={isReadOnly || isLoading}>
            {(provided, snapshot) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className={cn(
                  "space-y-4",
                  snapshot.isDraggingOver && "bg-base-200/50"
                )}
              >
                {localQuestions?.map((question, index) => (
                  <Draggable
                    key={`question-${index}`}
                    draggableId={`question-${index}`}
                    index={index}
                    isDragDisabled={isReadOnly || isLoading}
                  >
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className={cn(
                          "mb-4 md:mb-8 p-4 md:p-6 bg-white border border-gray-200 rounded-xl shadow-lg relative",
                          snapshot.isDragging && "shadow-2xl rotate-2 scale-105",
                          isLoading && "opacity-50"
                        )}
                      >
                        {/* Question Header with drag handle and action buttons */}
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 md:mb-5 pb-2 md:pb-3 border-b border-gray-200">
                          <div className="flex items-center gap-2 mb-2 sm:mb-0">
                            {/* Drag Handle - Always show */}
                            {!isReadOnly && (
                              <div
                                {...provided.dragHandleProps}
                                className="cursor-grab active:cursor-grabbing p-1 hover:bg-gray-100 rounded"
                              >
                                <GripVertical className="w-4 h-4 text-gray-400" />
                              </div>
                            )}

                            <span className="text-lg md:text-xl font-semibold text-gray-700">
                              Question {index + 1}
                            </span>

                            {/* Compact Reorder Buttons - Always show */}
                            {!isReadOnly && (
                              <div className="join">
                                <button
                                  onClick={() => {
                                    console.log('⬆️ Move up button clicked for index:', index);
                                    handleMoveUp(index);
                                  }}
                                  disabled={index === 0 || isLoading}
                                  className="btn btn-xs btn-square btn-ghost join-item"
                                  title="Move question up"
                                >
                                  <ChevronUp className="w-3 h-3" />
                                </button>
                                <button
                                  onClick={() => {
                                    console.log('⬇️ Move down button clicked for index:', index);
                                    handleMoveDown(index);
                                  }}
                                  disabled={index === localQuestions.length - 1 || isLoading}
                                  className="btn btn-xs btn-square btn-ghost join-item"
                                  title="Move question down"
                                >
                                  <ChevronDown className="w-3 h-3" />
                                </button>
                              </div>
                            )}
                          </div>
                          <div className="flex items-center gap-2 md:gap-3">
                            {question.subject && (
                              <span className="text-xs px-2 md:px-3 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
                                {question.subject}
                              </span>
                            )}
                            <span className="text-xs px-2 md:px-3 py-1 bg-green-100 text-green-700 rounded-full font-medium capitalize">
                              {question.type.replace('_', ' ')}
                            </span>

                            {/* Streamlined Action Bar - Edit & Delete Only */}
                            {!isReadOnly && (
                              <div className="flex items-center gap-1">
                                {/* Edit Button */}
                                <Button
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    console.log('Edit button clicked for question:', question);
                                    handleEditQuestion(question);
                                  }}
                                  disabled={isLoading}
                                  className="p-1.5 w-8 h-8 min-w-0 hover:bg-blue-50 hover:text-blue-600 rounded-md"
                                  title="Edit question"
                                >
                                  <Edit className="w-4 h-4" />
                                </Button>

                                {/* Delete Button */}
                                <Button
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    console.log('Delete button clicked for question:', question);
                                    handleDeleteQuestion(question);
                                  }}
                                  disabled={deletingQuestionId === question.id}
                                  className="p-1.5 w-8 h-8 min-w-0 hover:bg-red-50 hover:text-red-600 rounded-md"
                                  title="Delete question"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Question Content */}
                        <div className="space-y-3 md:space-y-4">
                          {/* Question Text */}
                          <div className="text-base md:text-lg leading-relaxed text-gray-800">
                            {isHtmlContent ? (
                              <div
                                className="max-w-none"
                                dangerouslySetInnerHTML={{
                                  __html: question.content,
                                }}
                              />
                            ) : (
                              <div>
                                {question.content.split('\n').map((paragraph, i) =>
                                  paragraph.trim() ? (
                                    <p key={i} className="mb-2 md:mb-3">
                                      {paragraph}
                                    </p>
                                  ) : null
                                )}
                              </div>
                            )}
                          </div>

                          {/* Question Image */}
                          {question.image && (
                            <div className="my-3 md:my-4 flex justify-center">
                              <QuestionImageRenderer
                                imageContent={question.image}
                                className="w-80 md:w-96 lg:w-[500px] h-auto rounded-lg shadow-sm border border-gray-200 overflow-hidden [&>svg]:w-full [&>svg]:h-auto [&>svg]:max-h-80 [&>svg]:md:max-h-96 [&>svg]:lg:max-h-[500px]"
                              />
                            </div>
                          )}

                          {/* Fill in the Blank */}
                          {question.type === 'fill_blank' && (
                            <div className="p-3 bg-gray-100 rounded-lg">
                              <p className="text-sm text-gray-600">Fill in the blank question</p>
                              <p className="text-sm">Answers: {question.answer?.join(', ')}</p>
                            </div>
                          )}

                          {/* Multiple Choice Options */}
                          {(question.type === 'multiple_choice' || question.type === 'single_choice' || question.type === 'true_false') &&
                            question.options && (
                              <div className="space-y-2 md:space-y-3">
                                {question.options.map((option, optionIndex) => {
                                  const optionLetter = String.fromCharCode(65 + optionIndex);
                                  const isAnswer = question.answer?.includes(option);

                                  return (
                                    <label
                                      key={optionIndex}
                                      className={cn(
                                        'flex items-start gap-3 p-3 md:p-4 rounded-lg border-2 transition-all cursor-pointer',
                                        isAnswer
                                          ? 'border-primary bg-primary/5'
                                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                                      )}
                                    >
                                      <div
                                        className={cn(
                                          'flex-shrink-0 w-6 h-6 md:w-7 md:h-7 rounded-full border-2 flex items-center justify-center text-xs md:text-sm font-medium',
                                          isAnswer
                                            ? 'border-primary bg-primary text-white'
                                            : 'border-gray-300 bg-white text-gray-600'
                                        )}
                                      >
                                        {optionLetter}
                                      </div>
                                      {isHtmlContent ? (
                                        <span
                                          className={cn(
                                            'flex-1 pt-0 md:pt-0.5 text-sm md:text-base leading-snug md:leading-relaxed',
                                            isAnswer && 'font-medium text-primary'
                                          )}
                                          dangerouslySetInnerHTML={{ __html: option }}
                                        />
                                      ) : (
                                        <span
                                          className={cn(
                                            'flex-1 pt-0 md:pt-0.5 text-sm md:text-base leading-snug md:leading-relaxed',
                                            isAnswer && 'font-medium text-primary'
                                          )}
                                        >
                                          {option}
                                        </span>
                                      )}
                                    </label>
                                  );
                                })}
                              </div>
                            )}

                          {/* Creative Writing */}
                          {question.type === 'creative_writing' && (
                            <div className="p-3 bg-gray-100 rounded-lg">
                              <p className="text-sm text-gray-600">Creative writing question</p>
                              {question.prompt && <p className="text-sm">Prompt: {question.prompt}</p>}
                              {question.answer && <p className="text-sm">Answer: {question.answer.join(', ')}</p>}
                            </div>
                          )}

                          {/* Explanation Accordion */}
                          {question?.explain && (
                            <ExplanationAccordion
                              explanation={question.explain}
                              isHtmlContent={isHtmlContent}
                            />
                          )}
                        </div>
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </div>

      {/* Delete Question Modal */}
      <DeleteQuestionModal
        isOpen={showDeleteModal}
        onClose={cancelDeleteQuestion}
        onConfirm={confirmDeleteQuestion}
        questionNumber={questionToDelete ? localQuestions.findIndex(q => q.id === questionToDelete.id) + 1 : 1}
        isLoading={deletingQuestionId !== null}
      />

      {/* Edit Question Modal */}
      {editingQuestion && (
        <QuestionFormModal
          isOpen={showEditModal}
          onClose={cancelEditQuestion}
          onSuccess={handleEditSuccess}
          worksheetId={worksheetId || 'temp-worksheet'}
          questionTypes={[
            { value: 'MULTIPLE_CHOICE', label: 'Multiple Choice' },
            { value: 'TRUE_FALSE', label: 'True/False' },
            { value: 'FILL_IN_BLANK', label: 'Fill in the Blank' },
            { value: 'SHORT_ANSWER', label: 'Short Answer' },
            { value: 'ESSAY', label: 'Essay' },
          ]}
          mode="edit"
          initialData={{
            id: editingQuestion.id || `temp-${Date.now()}`,
            type: editingQuestion.type.toUpperCase().replace(/_/g, '_') as any,
            content: editingQuestion.content,
            options: editingQuestion.options || [],
            answer: editingQuestion.answer || [],
            explain: editingQuestion.explain || '',
            imageUrl: editingQuestion.image || undefined,
            imagePrompt: editingQuestion.imagePrompt || undefined,
            subject: editingQuestion.subject,
            position: localQuestions.findIndex(q => q.id === editingQuestion.id) + 1,
            createdAt: new Date(),
            updatedAt: new Date(),
            version: 1,
          }}
        />
      )}
    </>
  );
};

// Question Image Renderer Component
type QuestionImageRendererProps = {
  imageContent: string;
  className?: string;
};

const QuestionImageRenderer: React.FC<QuestionImageRendererProps> = ({
  imageContent,
  className = '',
}) => {
  const [hasError, setHasError] = useState(false);

  // Check if the content looks like valid SVG
  const isValidSvg = (content: string): boolean => {
    const trimmed = content.trim();
    return trimmed.startsWith('<svg') && trimmed.includes('</svg>');
  };

  // Aggressively clean and fix SVG issues - targeting specific malformation patterns
  const cleanSvgContent = (content: string): string => {
    let cleaned = content.trim();

    // Step 1: Fix fragmented attributes - MOST CRITICAL
    // Pattern: attribute="\&quot;value1" value2="" value3="" value4\"=""
    cleaned = cleaned.replace(/([a-zA-Z-]+)="\\&quot;([^"]*)"([^=]*?)"([^=]*?)"([^"]*?)\\"=""/g, (_, attr, val1, val2, val3, val4) => {
      const fullValue = `${val1} ${val2} ${val3} ${val4}`.replace(/\s+/g, ' ').trim();
      return `${attr}="${fullValue}"`;
    });

    // Step 2: Fix simpler fragmented attributes
    // Pattern: attribute="\&quot;value\"=""
    cleaned = cleaned.replace(/([a-zA-Z-]+)="\\&quot;([^"]*)\\"=""/g, '$1="$2"');

    // Step 3: Fix font-family specifically (handles commas)
    // Pattern: font-family="\&quot;Arial," sans-serif\"=""
    cleaned = cleaned.replace(/font-family="\\&quot;([^"]*?),"([^"]*?)\\"=""/g, 'font-family="$1, $2"');

    // Step 4: Fix path data fragmentation
    // Pattern: d="\&quot;M60,130" l420,130\"=""
    cleaned = cleaned.replace(/d="\\&quot;([^"]*)"([^"]*?)\\"=""/g, 'd="$1 $2"');

    // Step 5: Fix stroke-dasharray fragmentation
    // Pattern: stroke-dasharray="\&quot;4" 4\"=""
    cleaned = cleaned.replace(/stroke-dasharray="\\&quot;([^"]*)"([^"]*?)\\"=""/g, 'stroke-dasharray="$1 $2"');

    // Step 6: Fix points attribute fragmentation
    // Pattern: points="\&quot;420,320" 410,315="" 415,325\"=""
    cleaned = cleaned.replace(/points="\\&quot;([^"]*)"([^=]*?)"([^=]*?)\\"=""/g, (_, val1, val2, val3) => {
      const fullValue = `${val1} ${val2} ${val3}`.replace(/\s+/g, ' ').trim();
      return `points="${fullValue}"`;
    });

    // Step 7: Now fix remaining double-encoded quotes
    cleaned = cleaned
      .replace(/\\&quot;/g, '"')  // Fix \&quot; to "
      .replace(/&quot;/g, '"')   // Fix &quot; to "
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&');

    // Step 8: Fix self-closing tags - convert "/> to "/>
    cleaned = cleaned.replace(/"\s*\/>/g, '"/>');
    cleaned = cleaned.replace(/([^\/])>/g, (match, char) => {
      // Only convert to self-closing if it's a self-closing element
      if (match.includes('rect') || match.includes('circle') || match.includes('line') || match.includes('path')) {
        return char + '/>';
      }
      return match;
    });

    // Step 9: Remove multiple consecutive closing tags
    cleaned = cleaned.replace(/<\/[^>]*>(<\/[^>]*>)+/g, '');

    // Step 10: Fix unclosed self-closing tags for specific elements
    cleaned = cleaned.replace(/<(rect|circle|line|path)([^>]*[^\/])>/g, '<$1$2/>');

    // Step 11: Clean up spacing
    cleaned = cleaned.replace(/\s+/g, ' ').replace(/>\s+</g, '><');

    // Step 12: Ensure proper SVG namespace
    if (!cleaned.includes('xmlns=')) {
      cleaned = cleaned.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"');
    }

    // Step 13: Final attribute cleanup
    cleaned = cleaned.replace(/="\s*([^"]*)\s*"/g, '="$1"');

    return cleaned;
  };

  const handleError = () => {
    setHasError(true);
  };

  // If it's a valid SVG, try to render it
  if (isValidSvg(imageContent) && !hasError) {
    const cleanedContent = cleanSvgContent(imageContent);

    return (
      <div
        className={className}
        onError={handleError}
        dangerouslySetInnerHTML={{ __html: cleanedContent }}
      />
    );
  }

  // Fallback: Check if it's a URL (for backward compatibility)
  if (imageContent.startsWith('http') || imageContent.startsWith('data:image')) {
    return (
      <img
        src={imageContent}
        alt="Question illustration"
        className={className}
        loading="lazy"
        onError={handleError}
      />
    );
  }

  // Final fallback: Show a placeholder for broken content
  return (
    <div className={cn(className, "flex items-center justify-center bg-gray-100 text-gray-500 text-sm")}>
      <div className="text-center p-4">
        <div className="mb-2">📷</div>
        <div>Image not available</div>
      </div>
    </div>
  );
};

// Explanation Accordion Component
type ExplanationAccordionProps = {
  explanation: string;
  isHtmlContent?: boolean;
};

const ExplanationAccordion: React.FC<ExplanationAccordionProps> = ({
  explanation,
  isHtmlContent = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mt-3 md:mt-5 border border-gray-200 rounded-md overflow-hidden shadow-sm">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between py-2 md:py-2.5 px-3 md:px-4 bg-gray-50 focus:outline-none transition-colors hover:bg-gray-100"
      >
        <div className="flex items-center gap-2">
          <ChevronDown
            className={cn(
              'w-4 h-4 transition-transform duration-200 text-primary',
              isOpen && 'rotate-180'
            )}
          />
          <span className="font-medium text-primary text-xs md:text-sm">
            View Explanation
          </span>
        </div>
      </button>

      <div
        className={cn(
          'transition-all duration-300 ease-in-out overflow-hidden',
          isOpen ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
        )}
      >
        <div className="p-3 md:p-4 bg-white border-t border-gray-200">
          {isHtmlContent ? (
            <div
              className="text-gray-700 text-xs md:text-sm leading-relaxed max-w-none"
              dangerouslySetInnerHTML={{
                __html: explanation,
              }}
            />
          ) : (
            <div className="text-gray-700 text-xs md:text-sm leading-relaxed">
              {explanation.split('\n').map((paragraph, i) =>
                paragraph.trim() ? (
                  <p key={i} className="mb-2 md:mb-3">
                    {paragraph}
                  </p>
                ) : null
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuestionListingView;
