'use client';

import * as React from 'react';
import Link from 'next/link';
import { cn } from '@/utils/cn';
import Icon, { IconProps } from '@/components/atoms/Icon/Icon';
import { cva, type VariantProps } from 'class-variance-authority';

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  isLoading?: boolean; // Optional prop to show a loading state
  href?: string; // Optional href for redirection
  iconProps?: IconProps; // Props for rendering an optional icon
}

// Define button variants using `cva`
const buttonVariants = cva('btn px-2 py-2 md:px-3 md:py-3 min-w-[6rem]', {
  variants: {
    variant: {
      primary: 'btn-primary bg-primary-action text-background-default hover:opacity-90',
      secondary: 'btn-secondary bg-text-secondary text-background-default hover:bg-text-primary',
      accent: 'btn-accent bg-link-default text-background-default hover:bg-link-hover',
      ghost: 'btn-ghost text-text-primary hover:bg-background-subtle',
      outline: 'btn-outline border-text-secondary text-text-secondary hover:bg-text-secondary hover:text-background-default',
      error: 'btn-error',
      pill: 'bg-button-pill-bg text-button-pill-text hover:opacity-90 rounded-full px-3 py-1.5 md:px-4 md:py-2',
    },
  },
  defaultVariants: {
    variant: 'primary',
  },
});

const Button: React.FC<ButtonProps> = ({
  children,
  className,
  isLoading = false,
  href,
  iconProps,
  variant,
  ...props
}) => {
  const content = (
    <>
      {isLoading && (
        <span className="absolute left-4 inline-flex h-4 w-4 animate-spin rounded-full border-2 border-t-transparent border-white"></span>
      )}
      {iconProps && (
        <Icon
          {...iconProps}
          className={cn('mr-2', iconProps.className)} // Add spacing for the icon
        />
      )}
      <span
        className={cn(
          isLoading ? 'opacity-0' : '',
          'text-sm font-medium transition-all'
        )}
      >
        {children}
      </span>
    </>
  );

  if (href) {
    // Use Next.js Link for redirection if href is provided
    return (
      <Link
        href={href}
        className={cn(
          'inline-flex items-center justify-center',
          buttonVariants({ variant }),
          className, // Ensure custom className is applied after buttonVariants
          isLoading && 'opacity-75 cursor-not-allowed'
        )}
      >
        {content}
      </Link>
    );
  }

  // Render a native button if no href is provided
  return (
    <button
      className={cn(
        'inline-flex items-center justify-center',
        buttonVariants({ variant }),
        className, // Ensure custom className is applied after buttonVariants
        isLoading && 'opacity-75 cursor-not-allowed'
      )}
      {...props}
    >
      {content}
    </button>
  );
};

export { Button, buttonVariants };
