/* Question Form Modal Improvements */

/* Tighten label spacing globally for the modal */
.modal-box .form-control .label {
  padding-bottom: 0.25rem;
  padding-top: 0;
  min-height: auto;
}

/* Reduce excessive spacing in form sections */
.modal-box .space-y-4 > * + * {
  margin-top: 1rem;
}

.modal-box .space-y-5 > * + * {
  margin-top: 1.25rem;
}

.modal-box .space-y-6 > * + * {
  margin-top: 1rem;
}

/* Balance the two-column layout */
.modal-box .grid[class*="lg:grid-cols-2"] {
  gap: 1.25rem;
}

/* Improve visual hierarchy with better section spacing */
.modal-box .bg-base-50 {
  padding: 0.875rem;
}

/* Tighten rich text editor spacing */
.modal-box .rich-text-editor {
  margin-top: 0.25rem;
}

/* Improve answer options section balance */
.modal-box .bg-base-50.space-y-2 {
  padding: 0.75rem;
}

.modal-box [class*="bg-success/5"] {
  padding: 0.75rem;
}

/* Better spacing for option items */
.modal-box .flex.items-center.gap-3 {
  gap: 0.75rem;
  padding: 0.375rem 0;
}

/* Improve checkbox/radio alignment */
.modal-box .checkbox,
.modal-box .radio {
  margin-top: 0;
  margin-bottom: 0;
}

/* Tighten input field spacing */
.modal-box .input,
.modal-box .select {
  margin-top: 0.25rem;
}

/* Better error message positioning */
.modal-box .label.py-0 {
  padding-top: 0.125rem;
  padding-bottom: 0;
}

/* Improve modal content balance */
.modal-box {
  padding: 1.5rem;
}

/* Better header spacing */
.modal-box .mb-4 {
  margin-bottom: 1rem;
}

/* Improve action button area */
.modal-box .pt-4.border-t {
  padding-top: 1rem;
  margin-top: 1rem;
}

/* Better visual separation for sections */
.modal-box .border.border-base-200 {
  border-color: hsl(var(--bc) / 0.15);
}

/* Improve form control consistency */
.modal-box .form-control + .form-control {
  margin-top: 1rem;
}

/* Better spacing for nested form elements */
.modal-box .form-control .space-y-2 > * + * {
  margin-top: 0.5rem;
}

/* Improve button spacing in options */
.modal-box .btn-xs {
  padding: 0.25rem;
}

/* Better alignment for option labels */
.modal-box .flex.items-center.gap-3.p-2 {
  padding: 0.5rem;
  gap: 0.75rem;
}

/* Improve mobile responsiveness */
@media (max-width: 1024px) {
  .modal-box .grid[class*="lg:grid-cols-2"] {
    gap: 1rem;
  }
  
  .modal-box .space-y-4 > * + * {
    margin-top: 0.875rem;
  }
}

@media (max-width: 768px) {
  .modal-box {
    padding: 1rem;
  }
  
  .modal-box .bg-base-50 {
    padding: 0.75rem;
  }
  
  .modal-box .space-y-4 > * + * {
    margin-top: 0.75rem;
  }
}

/* Improve visual balance between columns */
.modal-box .grid[class*="lg:grid-cols-2"] > div:first-child {
  /* Left column adjustments */
}

.modal-box .grid[class*="lg:grid-cols-2"] > div:last-child {
  /* Right column adjustments */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* Better spacing for rich text editors */
.modal-box .rich-text-editor .ql-container {
  margin-top: 0;
}

.modal-box .rich-text-editor .ql-toolbar {
  margin-bottom: 0;
}

/* Improve label text hierarchy */
.modal-box .label-text {
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.modal-box .label-text-alt {
  font-size: 0.75rem;
  line-height: 1rem;
  margin-top: 0.125rem;
}

/* Better visual grouping */
.modal-box .form-control {
  position: relative;
}

/* Improve focus states */
.modal-box .input:focus,
.modal-box .select:focus {
  outline: 2px solid hsl(var(--p));
  outline-offset: 2px;
}

/* Better spacing for add option button */
.modal-box .pt-2.border-t {
  padding-top: 0.5rem;
  margin-top: 0.5rem;
}

/* Improve overall form rhythm */
.modal-box form {
  line-height: 1.5;
}

/* Better alignment for circular badges */
.modal-box .w-6.h-6.rounded-full {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Improve text input consistency */
.modal-box .input-sm {
  height: 2rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
}

.modal-box .select-sm {
  height: 2rem;
  padding-left: 0.75rem;
  padding-right: 2rem;
  font-size: 0.875rem;
}

/* Delete Modal Specific Styles */
.modal-box .alert {
  margin-bottom: 0.75rem;
}

.modal-box .alert.alert-error {
  margin-bottom: 0.75rem;
}

.modal-box .alert.alert-warning {
  margin-bottom: 1rem;
}

/* Question preview section */
.modal-box .bg-base-50.border {
  margin-bottom: 1rem;
}

/* Textarea styling consistency */
.modal-box .textarea {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.5;
}

.modal-box .textarea:focus {
  outline: 2px solid hsl(var(--p));
  outline-offset: 2px;
}

/* Badge styling improvements */
.modal-box .badge {
  font-size: 0.75rem;
}

.modal-box .badge-outline {
  border-color: hsl(var(--bc) / 0.2);
}

/* Better spacing for delete modal content */
.modal-box .max-w-md {
  padding: 1.5rem;
}

/* Improve button consistency with edit modal */
.modal-box .btn-sm {
  height: 2rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
  min-height: 2rem;
}

/* Character counter styling */
.modal-box .label-text-alt {
  color: hsl(var(--bc) / 0.6);
  font-size: 0.75rem;
}
